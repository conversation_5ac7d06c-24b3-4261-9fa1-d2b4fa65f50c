---
inclusion: always
---

# Development Guidelines & Village Context

## Critical Development Rules
- **Never auto-start servers** - Do not execute `php artisan serve`, `npm run dev` `npm run build`, or `composer dev` without explicit user request
- **Indonesian content only** - All user-facing text, labels, and content must be in Bahasa Indonesia
- **Mobile-first design** - Prioritize smartphone users in all UI decisions
- **Respectful cultural tone** - Use formal, respectful language when referencing village traditions and legends
- **Code Format** - always run "npm run format && npm run lint && npm run types" to makesure there's no error fix if there any and keep the code clean
- **Reusable Code** - always use already existed code/file and if the file is at the wrong place/folder move it to the correct place/folder 

## Village Profile Context - Desa Lemah Duhur

### Basic Information
- **Full Address**: Desa Lemah Duhur, Kecamatan Caringin, Kabupaten Bogor, Jawa Barat
- **Established**: 1910-1920
- **Maps Link**: https://maps.app.goo.gl/F2SWrp4QBYBJXiG6A

### Etymology & Legend
- **Name Origin**: "Lemah" (level ground) + "Duhur/Luhur" (elevated area)
- **Historical Process**: "Lelemah di luhur" - leveling mountainous highlands for settlements
- **Village Legend**: Mystical figures used the area as landing ground for flying horses
- **Administrative History**: Originally larger area, later split into Desa Cimande Jaya and Desa Lemah Duhur

## Content Creation Guidelines
- Reference village history and cultural context in profile sections
- Use traditional Indonesian village terminology appropriately
- Maintain dignified tone when discussing local legends and customs
- Include geographical and administrative context where relevant