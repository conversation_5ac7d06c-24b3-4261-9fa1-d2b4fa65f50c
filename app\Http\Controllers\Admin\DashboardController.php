<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\News;
use App\Models\Potential;
use App\Models\Profile;
use App\Models\Service;
use App\Models\User;
use Carbon\Carbon;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     */
    public function index(): Response
    {
        // Basic content statistics
        $stats = [
            'total_news' => News::count(),
            'published_news' => News::where('is_published', true)->count(),
            'draft_news' => News::where('is_published', false)->count(),
            'total_services' => Service::count(),
            'active_services' => Service::where('is_active', true)->count(),
            'inactive_services' => Service::where('is_active', false)->count(),
            'total_potentials' => Potential::count(),
            'featured_potentials' => Potential::where('is_featured', true)->count(),
            'tourism_potentials' => Potential::where('type', 'tourism')->count(),
            'umkm_potentials' => Potential::where('type', 'umkm')->count(),
            'total_users' => User::count(),
            'profile_sections' => Profile::count(),
        ];

        // Recent content for quick overview
        $recent_news = News::latest()
            ->take(5)
            ->select('id', 'title', 'slug', 'is_published', 'created_at', 'category')
            ->get();

        $recent_potentials = Potential::latest()
            ->take(3)
            ->select('id', 'name', 'type', 'is_featured', 'created_at')
            ->get();

        // Content activity for the last 30 days
        $thirtyDaysAgo = Carbon::now()->subDays(30);
        $activity_stats = [
            'news_this_month' => News::where('created_at', '>=', $thirtyDaysAgo)->count(),
            'services_updated' => Service::where('updated_at', '>=', $thirtyDaysAgo)->count(),
            'potentials_added' => Potential::where('created_at', '>=', $thirtyDaysAgo)->count(),
        ];

        // Quick actions data
        $quick_actions = [
            [
                'title' => 'Tulis Berita Baru',
                'description' => 'Buat artikel berita terbaru untuk desa',
                'href' => route('admin.news.create'),
                'icon' => 'FileText',
                'color' => 'blue',
                'count' => $stats['draft_news'],
                'label' => 'draft tersimpan',
            ],
            [
                'title' => 'Kelola Layanan',
                'description' => 'Update informasi layanan publik desa',
                'href' => route('admin.services.manage'),
                'icon' => 'Settings',
                'color' => 'green',
                'count' => $stats['active_services'],
                'label' => 'layanan aktif',
            ],
            [
                'title' => 'Update Potensi',
                'description' => 'Kelola wisata dan UMKM desa',
                'href' => route('admin.potentials.manage'),
                'icon' => 'MapPin',
                'color' => 'purple',
                'count' => $stats['featured_potentials'],
                'label' => 'potensi unggulan',
            ],
            [
                'title' => 'Edit Profil Desa',
                'description' => 'Update informasi profil dan sejarah desa',
                'href' => route('admin.profile.manage'),
                'icon' => 'Building',
                'color' => 'orange',
                'count' => $stats['profile_sections'],
                'label' => 'bagian profil',
            ],
        ];

        return Inertia::render('Admin/dashboard', [
            'stats' => $stats,
            'activity_stats' => $activity_stats,
            'recent_news' => $recent_news,
            'recent_potentials' => $recent_potentials,
            'quick_actions' => $quick_actions,
        ]);
    }
}
