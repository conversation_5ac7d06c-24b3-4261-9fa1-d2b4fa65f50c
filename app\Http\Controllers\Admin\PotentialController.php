<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Potential;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class PotentialController extends Controller
{
    /**
     * Display a listing of potentials for admin management
     */
    public function index(Request $request)
    {
        $query = Potential::query()->latest('created_at');

        // Apply type filter
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Apply featured filter
        if ($request->filled('featured')) {
            if ($request->featured === 'yes') {
                $query->where('is_featured', true);
            } elseif ($request->featured === 'no') {
                $query->where('is_featured', false);
            }
        }

        // Apply search filter
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                    ->orWhere('description', 'like', "%{$searchTerm}%")
                    ->orWhere('location', 'like', "%{$searchTerm}%");
            });
        }

        // Get paginated results
        $potentials = $query->select([
            'id', 'name', 'type', 'description', 'location',
            'is_featured', 'images', 'created_at', 'updated_at',
        ])->paginate(10);

        return Inertia::render('Admin/Potentials/Index', [
            'potentials' => $potentials,
            'filters' => [
                'type' => $request->type,
                'featured' => $request->featured,
                'search' => $request->search,
            ],
        ]);
    }

    /**
     * Show the form for creating a new potential
     */
    public function create()
    {
        return Inertia::render('Admin/Potentials/Create');
    }

    /**
     * Store a newly created potential
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:tourism,umkm',
            'description' => 'required|string',
            'location' => 'nullable|string|max:255',
            'contact_info' => 'nullable|array',
            'contact_info.phone' => 'nullable|string|max:20',
            'contact_info.email' => 'nullable|email|max:100',
            'contact_info.person' => 'nullable|string|max:100',
            'contact_info.address' => 'nullable|string|max:255',
            'contact_info.website' => 'nullable|url|max:255',
            'images' => 'nullable|array|max:10',
            'images.*' => 'image|mimes:jpeg,png,jpg,webp|max:2048',
            'is_featured' => 'boolean',
        ]);

        // Handle image uploads
        $imagePaths = [];
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('potentials', 'public');
                $imagePaths[] = $path;
            }
        }
        $validated['images'] = $imagePaths;

        // Clean up contact_info - remove empty values
        if (isset($validated['contact_info'])) {
            $validated['contact_info'] = array_filter($validated['contact_info'], function ($value) {
                return !in_array(trim($value), ['', '0'], true);
            });

            if (empty($validated['contact_info'])) {
                $validated['contact_info'] = null;
            }
        }

        Potential::create($validated);

        return redirect()->route('admin.potentials.index')
            ->with('success', 'Potensi berhasil dibuat.');
    }

    /**
     * Display the specified potential for viewing
     */
    public function show(Potential $potential)
    {
        return Inertia::render('Admin/Potentials/Show', [
            'potential' => $potential,
        ]);
    }

    /**
     * Show the form for editing the specified potential
     */
    public function edit(Potential $potential)
    {
        return Inertia::render('Admin/Potentials/Edit', [
            'potential' => $potential,
        ]);
    }

    /**
     * Update the specified potential
     */
    public function update(Request $request, Potential $potential)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:tourism,umkm',
            'description' => 'required|string',
            'location' => 'nullable|string|max:255',
            'contact_info' => 'nullable|array',
            'contact_info.phone' => 'nullable|string|max:20',
            'contact_info.email' => 'nullable|email|max:100',
            'contact_info.person' => 'nullable|string|max:100',
            'contact_info.address' => 'nullable|string|max:255',
            'contact_info.website' => 'nullable|url|max:255',
            'new_images' => 'nullable|array|max:10',
            'new_images.*' => 'image|mimes:jpeg,png,jpg,webp|max:2048',
            'existing_images' => 'nullable|array',
            'existing_images.*' => 'string',
            'is_featured' => 'boolean',
        ]);

        // Handle existing images
        $existingImages = $validated['existing_images'] ?? [];

        // Delete removed images from storage
        $currentImages = $potential->images ?? [];
        foreach ($currentImages as $currentImage) {
            if (! in_array($currentImage, $existingImages)) {
                Storage::disk('public')->delete($currentImage);
            }
        }

        // Handle new image uploads
        $newImagePaths = [];
        if ($request->hasFile('new_images')) {
            foreach ($request->file('new_images') as $image) {
                $path = $image->store('potentials', 'public');
                $newImagePaths[] = $path;
            }
        }

        // Combine existing and new images
        $validated['images'] = array_merge($existingImages, $newImagePaths);

        // Clean up contact_info - remove empty values
        if (isset($validated['contact_info'])) {
            $validated['contact_info'] = array_filter($validated['contact_info'], function ($value) {
                return !in_array(trim($value), ['', '0'], true);
            });

            if (empty($validated['contact_info'])) {
                $validated['contact_info'] = null;
            }
        }

        // Remove fields that shouldn't be updated directly
        unset($validated['new_images'], $validated['existing_images']);

        $potential->update($validated);

        return redirect()->route('admin.potentials.index')
            ->with('success', 'Potensi berhasil diperbarui.');
    }

    /**
     * Remove the specified potential
     */
    public function destroy(Potential $potential)
    {
        // Delete associated images from storage
        if ($potential->images) {
            foreach ($potential->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $potential->delete();

        return redirect()->route('admin.potentials.index')
            ->with('success', 'Potensi berhasil dihapus.');
    }

    /**
     * Toggle featured status of potential
     */
    public function toggleFeatured(Potential $potential)
    {
        $potential->update([
            'is_featured' => ! $potential->is_featured,
        ]);

        $status = $potential->is_featured ? 'ditampilkan sebagai unggulan' : 'dihapus dari unggulan';

        return redirect()->back()
            ->with('success', "Potensi berhasil {$status}.");
    }
}
