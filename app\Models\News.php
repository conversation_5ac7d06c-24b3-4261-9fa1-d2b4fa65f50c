<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class News extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'content',
        'excerpt',
        'featured_image',
        'category',
        'published_at',
        'is_published',
        'meta_title',
        'meta_description',
    ];

    // Automatically generate slug from title
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($news) {
            if (empty($news->slug)) {
                $news->slug = Str::slug($news->title);
            }
        });

        static::updating(function ($news) {
            if ($news->isDirty('title') && (empty($news->slug) || $news->slug === Str::slug($news->getOriginal('title')))) {
                $news->slug = Str::slug($news->title);
            }
        });
    }

    // Scopes
    #[\Illuminate\Database\Eloquent\Attributes\Scope]
    protected function published($query)
    {
        return $query->where('is_published', true)
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now());
    }

    #[\Illuminate\Database\Eloquent\Attributes\Scope]
    protected function byCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    #[\Illuminate\Database\Eloquent\Attributes\Scope]
    protected function latestPublished($query)
    {
        return $query->orderByDesc('published_at');
    }

    // Accessors
    protected function excerpt(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ?: Str::limit(strip_tags($this->content), 147)
        );
    }

    protected function metaTitle(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ?: $this->title
        );
    }

    protected function metaDescription(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ?: $this->excerpt
        );
    }

    // Image helpers
    public function getFeaturedImageUrl($size = 'medium'): ?string
    {
        if (! $this->featured_image || ! is_array($this->featured_image)) {
            return null;
        }

        return $this->featured_image[$size]['url'] ?? $this->featured_image['medium']['url'] ?? $this->featured_image['original']['url'] ?? null;
    }

    public function getFeaturedImageSrcSet(): string
    {
        if (! $this->featured_image || ! is_array($this->featured_image)) {
            return '';
        }

        $srcSet = [];

        if (isset($this->featured_image['thumbnail']['url'])) {
            $srcSet[] = $this->featured_image['thumbnail']['url'].' 300w';
        }

        if (isset($this->featured_image['medium']['url'])) {
            $srcSet[] = $this->featured_image['medium']['url'].' 800w';
        }

        if (isset($this->featured_image['original']['url'])) {
            $srcSet[] = $this->featured_image['original']['url'].' 1200w';
        }

        return implode(', ', $srcSet);
    }
    protected function casts(): array
    {
        return [
            'published_at' => 'datetime',
            'is_published' => 'boolean',
            'featured_image' => 'array',
        ];
    }
}
