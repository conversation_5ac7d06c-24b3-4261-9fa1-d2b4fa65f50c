<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Potential extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type', // 'tourism', 'umkm'
        'description',
        'images',
        'contact_info',
        'location',
        'is_featured',
    ];

    // Scopes
    #[\Illuminate\Database\Eloquent\Attributes\Scope]
    protected function tourism($query)
    {
        return $query->where('type', 'tourism');
    }

    #[\Illuminate\Database\Eloquent\Attributes\Scope]
    protected function umkm($query)
    {
        return $query->where('type', 'umkm');
    }

    #[\Illuminate\Database\Eloquent\Attributes\Scope]
    protected function featured($query)
    {
        return $query->where('is_featured', true);
    }

    #[\Illuminate\Database\Eloquent\Attributes\Scope]
    protected function byType($query, $type)
    {
        return $query->where('type', $type);
    }

    protected function imageList(): \Illuminate\Database\Eloquent\Casts\Attribute
    {
        return \Illuminate\Database\Eloquent\Casts\Attribute::make(get: function () {
            return is_array($this->images) ? $this->images : [];
        });
    }

    protected function featuredImage(): \Illuminate\Database\Eloquent\Casts\Attribute
    {
        return \Illuminate\Database\Eloquent\Casts\Attribute::make(get: function () {
            $images = $this->image_list;
            return empty($images) ? null : $images[0];
        });
    }

    // Image helpers
    public function getImageUrl($index = 0, $size = 'medium'): ?string
    {
        $images = $this->image_list;

        if (! isset($images[$index]) || ! is_array($images[$index])) {
            return null;
        }

        $image = $images[$index];

        return $image[$size]['url'] ?? $image['medium']['url'] ?? $image['original']['url'] ?? null;
    }

    public function getImageSrcSet($index = 0): string
    {
        $images = $this->image_list;

        if (! isset($images[$index]) || ! is_array($images[$index])) {
            return '';
        }

        $image = $images[$index];
        $srcSet = [];

        if (isset($image['thumbnail']['url'])) {
            $srcSet[] = $image['thumbnail']['url'].' 300w';
        }

        if (isset($image['medium']['url'])) {
            $srcSet[] = $image['medium']['url'].' 800w';
        }

        if (isset($image['original']['url'])) {
            $srcSet[] = $image['original']['url'].' 1200w';
        }

        return implode(', ', $srcSet);
    }

    public function getAllImageUrls($size = 'medium'): array
    {
        $images = $this->image_list;
        $urls = [];

        foreach ($images as $image) {
            if (is_array($image)) {
                $url = $image[$size]['url'] ?? $image['medium']['url'] ?? $image['original']['url'] ?? null;
                if ($url) {
                    $urls[] = $url;
                }
            }
        }

        return $urls;
    }
    protected function typeName(): \Illuminate\Database\Eloquent\Casts\Attribute
    {
        return \Illuminate\Database\Eloquent\Casts\Attribute::make(get: function () {
            return match ($this->type) {
                'tourism' => 'Wisata',
                'umkm' => 'UMKM',
                default => ucfirst($this->type)
            };
        });
    }
    protected function contactList(): \Illuminate\Database\Eloquent\Casts\Attribute
    {
        return \Illuminate\Database\Eloquent\Casts\Attribute::make(get: function () {
            return is_array($this->contact_info) ? $this->contact_info : [];
        });
    }
    protected function casts(): array
    {
        return [
            'images' => 'array',
            'contact_info' => 'array',
            'is_featured' => 'boolean',
        ];
    }
}
