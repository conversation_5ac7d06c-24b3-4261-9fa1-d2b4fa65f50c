<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Service extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'requirements',
        'procedure',
        'cost',
        'processing_time',
        'contact_info',
        'is_active',
    ];

    // Scopes
    #[\Illuminate\Database\Eloquent\Attributes\Scope]
    protected function active($query)
    {
        return $query->where('is_active', true);
    }

    #[\Illuminate\Database\Eloquent\Attributes\Scope]
    protected function inactive($query)
    {
        return $query->where('is_active', false);
    }
    protected function formattedCost(): \Illuminate\Database\Eloquent\Casts\Attribute
    {
        return \Illuminate\Database\Eloquent\Casts\Attribute::make(get: function () {
            if (! $this->cost) {
                return 'Gratis';
            }
            if (is_numeric($this->cost)) {
                return 'Rp '.number_format($this->cost, 0, ',', '.');
            }
            return $this->cost;
        });
    }
    protected function requirementsList(): \Illuminate\Database\Eloquent\Casts\Attribute
    {
        return \Illuminate\Database\Eloquent\Casts\Attribute::make(get: function () {
            return is_array($this->requirements) ? $this->requirements : [];
        });
    }
    protected function procedureList(): \Illuminate\Database\Eloquent\Casts\Attribute
    {
        return \Illuminate\Database\Eloquent\Casts\Attribute::make(get: function () {
            return is_array($this->procedure) ? $this->procedure : [];
        });
    }
    protected function casts(): array
    {
        return [
            'requirements' => 'array',
            'procedure' => 'array',
            'contact_info' => 'array',
            'is_active' => 'boolean',
        ];
    }
}
