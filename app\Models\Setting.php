<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'value' => 'array',
        ];
    }

    /**
     * Get a configuration value by key with optional default
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        try {
            // Try to get from cache first for better performance
            $cacheKey = "setting_{$key}";
            
            return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
                $setting = static::where('key', $key)->first();
                
                if ($setting) {
                    return $setting->value;
                }
                
                return $default;
            });
        } catch (\Exception $e) {
            // Log error and return default value if database is unavailable
            Log::error("Failed to retrieve setting '{$key}': " . $e->getMessage());
            return $default;
        }
    }

    /**
     * Set a configuration value by key
     *
     * @param string $key
     * @param mixed $value
     * @param string|null $description
     * @return void
     */
    public static function set(string $key, mixed $value, ?string $description = null): void
    {
        try {
            static::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'type' => 'json',
                    'description' => $description,
                ]
            );

            // Clear cache after updating
            $cacheKey = "setting_{$key}";
            Cache::forget($cacheKey);
        } catch (\Exception $e) {
            Log::error("Failed to set setting '{$key}': " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check if a configuration key exists
     *
     * @param string $key
     * @return bool
     */
    public static function has(string $key): bool
    {
        try {
            return static::where('key', $key)->exists();
        } catch (\Exception $e) {
            Log::error("Failed to check setting existence '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Remove a configuration by key
     *
     * @param string $key
     * @return bool
     */
    public static function forget(string $key): bool
    {
        try {
            $deleted = static::where('key', $key)->delete();
            
            if ($deleted) {
                // Clear cache after deletion
                $cacheKey = "setting_{$key}";
                Cache::forget($cacheKey);
            }
            
            return $deleted > 0;
        } catch (\Exception $e) {
            Log::error("Failed to delete setting '{$key}': " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all settings as key-value pairs
     *
     * @return array
     */
    public static function getAllSettings(): array
    {
        try {
            return static::pluck('value', 'key')->toArray();
        } catch (\Exception $e) {
            Log::error("Failed to retrieve all settings: " . $e->getMessage());
            return [];
        }
    }
}