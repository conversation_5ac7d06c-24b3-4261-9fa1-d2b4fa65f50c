<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Operating hours configuration for Desa Lemah Duhur
        Setting::updateOrCreate(
            ['key' => 'village.operating_hours'],
            [
                'value' => [
                    'weekdays' => 'Senin - Jumat: 08:00 - 15:00 WIB',
                    'saturday' => 'Sabtu: 08:00 - 12:00 WIB',
                    'sunday' => 'Minggu: Tutup',
                    'break' => 'Istirahat: 12:00 - 13:00 WIB',
                    'holidays' => 'Hari libur nasional: Tutup'
                ],
                'type' => 'json',
                'description' => 'Jam operasional pelayanan Kantor <PERSON> untuk layanan administrasi kepada masyarakat'
            ]
        );

        // Contact information for Desa Lemah Duhur
        Setting::updateOrCreate(
            ['key' => 'village.contact_info'],
            [
                'value' => [
                    'phone' => '(0251) 8240xxx',
                    'whatsapp' => '081234567890',
                    'email' => 'desa.lemah<PERSON>@gmail.com',
                    'address' => '<PERSON><PERSON>r <PERSON>, Kec. Car<PERSON>in, Kab. Bogor, <PERSON>awa Barat',
                    'postal_code' => '16730',
                    'maps_link' => 'https://maps.app.goo.gl/F2SWrp4QBYBJXiG6A'
                ],
                'type' => 'json',
                'description' => 'Informasi kontak resmi Desa Lemah Duhur untuk keperluan komunikasi dan layanan masyarakat'
            ]
        );

        // Village profile information
        Setting::updateOrCreate(
            ['key' => 'village.profile'],
            [
                'value' => [
                    'name' => 'Desa Lemah Duhur',
                    'district' => 'Kecamatan Caringin',
                    'regency' => 'Kabupaten Bogor',
                    'province' => 'Jawa Barat',
                    'established_year' => '1910-1920',
                    'area' => 'Dataran tinggi pegunungan',
                    'population' => 'Data akan diperbarui sesuai sensus terbaru'
                ],
                'type' => 'json',
                'description' => 'Profil dasar Desa Lemah Duhur meliputi informasi geografis dan administratif'
            ]
        );

        // Village history and etymology
        Setting::updateOrCreate(
            ['key' => 'village.history'],
            [
                'value' => [
                    'etymology' => [
                        'lemah' => 'Tanah yang rata/datar',
                        'duhur_luhur' => 'Tempat yang tinggi/terhormat',
                        'meaning' => 'Proses perataan tanah di daerah pegunungan tinggi untuk pemukiman'
                    ],
                    'legend' => 'Menurut legenda setempat, daerah ini pernah digunakan sebagai tempat pendaratan kuda terbang oleh sosok-sosok mistis',
                    'administrative_history' => 'Awalnya merupakan wilayah yang lebih luas, kemudian terbagi menjadi Desa Cimande Jaya dan Desa Lemah Duhur',
                    'cultural_significance' => 'Memiliki nilai historis dan budaya yang tinggi dalam perkembangan wilayah Caringin'
                ],
                'type' => 'json',
                'description' => 'Sejarah, etimologi, dan legenda Desa Lemah Duhur yang mencerminkan kekayaan budaya lokal'
            ]
        );

        // Service availability settings
        Setting::updateOrCreate(
            ['key' => 'village.service_settings'],
            [
                'value' => [
                    'online_services_available' => true,
                    'mobile_optimized' => true,
                    'emergency_contact_available' => true,
                    'multilingual_support' => false,
                    'digital_signature_enabled' => false
                ],
                'type' => 'json',
                'description' => 'Pengaturan ketersediaan layanan digital dan fitur-fitur yang tersedia untuk masyarakat'
            ]
        );

        // Emergency and important contacts
        Setting::updateOrCreate(
            ['key' => 'village.emergency_contacts'],
            [
                'value' => [
                    'village_head' => [
                        'title' => 'Kepala Desa',
                        'phone' => '081234567891'
                    ],
                    'village_secretary' => [
                        'title' => 'Sekretaris Desa',
                        'phone' => '081234567892'
                    ],
                    'security' => [
                        'title' => 'Keamanan Desa (Hansip)',
                        'phone' => '081234567893'
                    ],
                    'health_center' => [
                        'title' => 'Puskesmas Caringin',
                        'phone' => '(0251) 8240xxx'
                    ]
                ],
                'type' => 'json',
                'description' => 'Kontak darurat dan penting untuk keperluan mendesak masyarakat Desa Lemah Duhur'
            ]
        );
    }
}