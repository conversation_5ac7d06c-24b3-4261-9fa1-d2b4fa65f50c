import { Button } from '@/components/ui/button';
import { Alert<PERSON>riangle, RefreshCw } from 'lucide-react';
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
    children: ReactNode;
    fallback?: ReactNode;
}

interface State {
    hasError: boolean;
    error?: Error;
    errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
    constructor(props: Props) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): State {
        // Update state so the next render will show the fallback UI
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        // Log error to console for debugging
        console.error('ErrorBoundary caught an error:', error, errorInfo);

        this.setState({
            error,
            errorInfo,
        });

        // Here you could also log the error to an error reporting service
        // Example: logErrorToService(error, errorInfo);
    }

    handleRetry = () => {
        this.setState({ hasError: false, error: undefined, errorInfo: undefined });
    };

    render() {
        if (this.state.hasError) {
            // Custom fallback UI
            if (this.props.fallback) {
                return this.props.fallback;
            }

            // Default fallback UI
            return (
                <div className="flex min-h-[400px] items-center justify-center bg-gray-50 px-4">
                    <div className="w-full max-w-md text-center">
                        <div className="mb-8">
                            <AlertTriangle className="mx-auto mb-4 h-16 w-16 text-red-500" />
                            <h2 className="mb-4 text-xl font-semibold text-gray-700">Terjadi Kesalahan</h2>
                            <p className="mb-6 text-gray-600">
                                Maaf, terjadi kesalahan yang tidak terduga. Silakan coba muat ulang halaman atau hubungi administrator jika masalah
                                berlanjut.
                            </p>
                        </div>

                        <div className="space-y-4">
                            <Button onClick={this.handleRetry} className="w-full">
                                <RefreshCw className="mr-2 h-4 w-4" />
                                Coba Lagi
                            </Button>

                            <Button onClick={() => window.location.reload()} variant="outline" className="w-full">
                                Muat Ulang Halaman
                            </Button>

                            <Button onClick={() => (window.location.href = '/')} variant="outline" className="w-full">
                                Ke Beranda
                            </Button>
                        </div>

                        {process.env.NODE_ENV === 'development' && this.state.error && (
                            <details className="mt-8 text-left">
                                <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">Detail Error (Development)</summary>
                                <div className="mt-4 rounded bg-red-50 p-4 text-xs text-red-800">
                                    <pre className="whitespace-pre-wrap">
                                        {this.state.error.toString()}
                                        {this.state.errorInfo?.componentStack}
                                    </pre>
                                </div>
                            </details>
                        )}
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}

// Hook version for functional components
export function withErrorBoundary<P extends object>(Component: React.ComponentType<P>, fallback?: ReactNode) {
    return function WrappedComponent(props: P) {
        return (
            <ErrorBoundary fallback={fallback}>
                <Component {...props} />
            </ErrorBoundary>
        );
    };
}
