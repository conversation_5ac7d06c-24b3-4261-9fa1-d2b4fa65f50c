import { Head } from '@inertiajs/react';

interface SEOHeadProps {
    title?: string;
    description?: string;
    image?: string;
    url?: string;
    type?: string;
    siteName?: string;
    locale?: string;
    publishedTime?: string;
    modifiedTime?: string;
    structuredData?: object;
    noIndex?: boolean;
    canonical?: string;
}

export default function SEOHead({
    title = 'Desa Lemah Duhur - Kecamatan Caringin, Kabupaten Bogor',
    description = 'Website resmi Desa Lemah Duhur, Kecamatan Caringin, Kabupaten Bogor, Jawa Barat. Informasi profil desa, layanan publik, berita terkini, dan potensi wisata serta UMKM.',
    image = '/images/logo-desa.png',
    url = 'https://lemah-duhur.desa.id',
    type = 'website',
    siteName = 'Desa Lemah Duhur',
    locale = 'id_ID',
    publishedTime,
    modifiedTime,
    structuredData,
    noIndex = false,
    canonical,
}: SEOHeadProps) {
    const fullImageUrl = image.startsWith('http') ? image : `${url}${image}`;
    const canonicalUrl = canonical || url;

    return (
        <Head>
            {/* Basic Meta Tags */}
            <title>{title}</title>
            <meta name="description" content={description} />
            <meta name="viewport" content="width=device-width, initial-scale=1.0" />
            <meta name="language" content="Indonesian" />
            <meta name="author" content="Pemerintah Desa Lemah Duhur" />

            {/* Robots Meta */}
            {noIndex ? <meta name="robots" content="noindex, nofollow" /> : <meta name="robots" content="index, follow" />}

            {/* Canonical URL */}
            <link rel="canonical" href={canonicalUrl} />

            {/* Open Graph Meta Tags */}
            <meta property="og:title" content={title} />
            <meta property="og:description" content={description} />
            <meta property="og:image" content={fullImageUrl} />
            <meta property="og:url" content={url} />
            <meta property="og:type" content={type} />
            <meta property="og:site_name" content={siteName} />
            <meta property="og:locale" content={locale} />

            {/* Article specific Open Graph tags */}
            {type === 'article' && publishedTime && <meta property="article:published_time" content={publishedTime} />}
            {type === 'article' && modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}

            {/* Twitter Card Meta Tags */}
            <meta name="twitter:card" content="summary_large_image" />
            <meta name="twitter:title" content={title} />
            <meta name="twitter:description" content={description} />
            <meta name="twitter:image" content={fullImageUrl} />

            {/* Additional Meta Tags for Indonesian Government Website */}
            <meta name="geo.region" content="ID-JB" />
            <meta name="geo.placename" content="Lemah Duhur, Caringin, Bogor" />
            <meta name="geo.position" content="-6.6175;106.8467" />
            <meta name="ICBM" content="-6.6175, 106.8467" />

            {/* Theme Color */}
            <meta name="theme-color" content="#059669" />
            <meta name="msapplication-TileColor" content="#059669" />

            {/* Structured Data */}
            {structuredData && (
                <script
                    type="application/ld+json"
                    dangerouslySetInnerHTML={{
                        __html: JSON.stringify(structuredData),
                    }}
                />
            )}

            {/* Favicon */}
            <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
            <link rel="icon" type="image/png" href="/favicon.ico" />
            <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

            {/* Preconnect to external domains for performance */}
            <link rel="preconnect" href="https://fonts.googleapis.com" />
            <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        </Head>
    );
}
