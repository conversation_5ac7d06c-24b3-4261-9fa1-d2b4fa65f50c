import { Footer } from '@/components/layout/Footer';
import { Header } from '@/components/layout/Header';
import { Head } from '@inertiajs/react';
import { ReactNode } from 'react';

interface PublicLayoutProps {
    children: ReactNode;
    title?: string;
    description?: string;
}

export default function PublicLayout({ children, title, description }: PublicLayoutProps) {
    const pageTitle = title ? `${title} - <PERSON><PERSON>hur` : 'Desa Lemah Duhur';

    return (
        <>
            <Head title={pageTitle}>
                <meta
                    name="description"
                    content={description || 'Website resmi Desa Lemah Duhur - Informasi profil, layanan, berita, dan potensi desa'}
                />
                <meta name="viewport" content="width=device-width, initial-scale=1" />
                <meta name="robots" content="index, follow" />
                <meta property="og:title" content={pageTitle} />
                <meta property="og:description" content={description || 'Website resmi Desa Lemah Duhur'} />
                <meta property="og:type" content="website" />
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>

            <div className="flex min-h-screen flex-col bg-background">
                <Header />
                <main className="flex-1">{children}</main>
                <Footer />
            </div>
        </>
    );
}
