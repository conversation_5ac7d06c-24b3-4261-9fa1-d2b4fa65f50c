import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem, type PaginatedData } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { CheckCircle, Edit, Eye, EyeOff, FileText, Plus, Search, Trash2, XCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

interface News {
    id: number;
    title: string;
    slug: string;
    excerpt: string;
    featured_image: string | null;
    category: string;
    is_published: boolean;
    published_at: string | null;
    created_at: string;
    updated_at: string;
}

interface Props {
    news: PaginatedData<News>;
    categories: string[];
    filters: {
        status?: string;
        category?: string;
        search?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Berita',
        href: '/admin/news',
    },
];

export default function NewsIndex({ news, categories, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const { delete: destroy } = useForm();

    // Handle search with debounce
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (searchTerm !== filters.search) {
                router.get(
                    '/admin/news',
                    {
                        status: filters.status,
                        category: filters.category,
                        search: searchTerm || undefined,
                    },
                    {
                        preserveState: true,
                        replace: true,
                    },
                );
            }
        }, 300);

        return () => clearTimeout(timeoutId);
    }, [searchTerm, filters.search, filters.status, filters.category]);

    const handleFilterChange = (key: string, value: string) => {
        router.get(
            '/admin/news',
            {
                ...filters,
                [key]: value || undefined,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleDelete = (newsItem: News) => {
        if (confirm(`Apakah Anda yakin ingin menghapus berita "${newsItem.title}"?`)) {
            destroy(route('admin.news.destroy', newsItem.id), {
                preserveScroll: true,
            });
        }
    };

    const handleTogglePublish = (newsItem: News) => {
        const action = newsItem.is_published ? 'jadikan draft' : 'publikasikan';
        if (confirm(`Apakah Anda yakin ingin ${action} berita "${newsItem.title}"?`)) {
            router.post(
                route('admin.news.toggle-publish', newsItem.id),
                {},
                {
                    preserveScroll: true,
                },
            );
        }
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Manajemen Berita - Admin Desa Lemah Duhur" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Manajemen Berita</h1>
                        <p className="text-gray-600">Kelola berita dan pengumuman desa</p>
                    </div>
                    <Link href={route('admin.news.create')}>
                        <Button className="flex items-center space-x-2">
                            <Plus className="h-4 w-4" />
                            <span>Tambah Berita</span>
                        </Button>
                    </Link>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Search className="h-5 w-5" />
                            <span>Filter & Pencarian</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Pencarian</label>
                                <Input placeholder="Cari judul atau konten..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Status</label>
                                <Select
                                    value={filters.status || 'all'}
                                    onValueChange={(value) => handleFilterChange('status', value === 'all' ? '' : value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Semua Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Status</SelectItem>
                                        <SelectItem value="published">Dipublikasikan</SelectItem>
                                        <SelectItem value="draft">Draft</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Kategori</label>
                                <Select
                                    value={filters.category || 'all'}
                                    onValueChange={(value) => handleFilterChange('category', value === 'all' ? '' : value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Semua Kategori" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Kategori</SelectItem>
                                        {categories.map((category) => (
                                            <SelectItem key={category} value={category}>
                                                {category}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-end">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setSearchTerm('');
                                        router.get('/admin/news', {}, { replace: true });
                                    }}
                                    className="w-full"
                                >
                                    Reset Filter
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* News Table */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <FileText className="h-5 w-5" />
                                <span>Daftar Berita ({news.total})</span>
                            </div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {news.data.length > 0 ? (
                            <div className="space-y-4">
                                <div className="overflow-x-auto">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Judul</TableHead>
                                                <TableHead>Kategori</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Tanggal</TableHead>
                                                <TableHead className="text-right">Aksi</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {news.data.map((newsItem) => (
                                                <TableRow key={newsItem.id}>
                                                    <TableCell>
                                                        <div className="space-y-1">
                                                            <div className="line-clamp-2 font-medium text-gray-900">{newsItem.title}</div>
                                                            {newsItem.excerpt && (
                                                                <div className="line-clamp-2 text-sm text-gray-500">{newsItem.excerpt}</div>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant="outline">{newsItem.category}</Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="flex items-center space-x-2">
                                                            {newsItem.is_published ? (
                                                                <>
                                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                                    <span className="text-sm text-green-600">Dipublikasikan</span>
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <XCircle className="h-4 w-4 text-gray-400" />
                                                                    <span className="text-sm text-gray-500">Draft</span>
                                                                </>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="space-y-1">
                                                            <div className="text-sm">
                                                                {newsItem.published_at
                                                                    ? new Date(newsItem.published_at).toLocaleDateString('id-ID')
                                                                    : 'Belum dipublikasikan'}
                                                            </div>
                                                            <div className="text-xs text-gray-500">
                                                                Dibuat: {new Date(newsItem.created_at).toLocaleDateString('id-ID')}
                                                            </div>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex items-center justify-end space-x-2">
                                                            <Link href={route('admin.news.preview', newsItem.id)}>
                                                                <Button variant="ghost" size="sm">
                                                                    <Eye className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Link href={route('admin.news.edit', newsItem.id)}>
                                                                <Button variant="ghost" size="sm">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Button variant="ghost" size="sm" onClick={() => handleTogglePublish(newsItem)}>
                                                                {newsItem.is_published ? (
                                                                    <EyeOff className="h-4 w-4" />
                                                                ) : (
                                                                    <CheckCircle className="h-4 w-4" />
                                                                )}
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => handleDelete(newsItem)}
                                                                className="text-red-600 hover:text-red-800"
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Pagination */}
                                {news.last_page > 1 && (
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm text-gray-500">
                                            Menampilkan {news.from} - {news.to} dari {news.total} berita
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {news.links.map((link, index) => (
                                                <Button
                                                    key={index}
                                                    variant={link.active ? 'default' : 'outline'}
                                                    size="sm"
                                                    disabled={!link.url}
                                                    onClick={() => {
                                                        if (link.url) {
                                                            router.get(link.url);
                                                        }
                                                    }}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="py-12 text-center">
                                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-4 text-lg font-medium text-gray-900">Belum ada berita</h3>
                                <p className="mt-2 text-gray-500">
                                    {Object.values(filters).some(Boolean)
                                        ? 'Tidak ada berita yang sesuai dengan filter yang dipilih.'
                                        : 'Mulai dengan membuat berita pertama untuk desa.'}
                                </p>
                                <div className="mt-6">
                                    <Link href={route('admin.news.create')}>
                                        <Button>
                                            <Plus className="mr-2 h-4 w-4" />
                                            Tambah Berita
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
