import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, Edit, Globe, ImageIcon, Mail, MapPin, Phone, Star, User } from 'lucide-react';

interface Potential {
    id: number;
    name: string;
    type: 'tourism' | 'umkm';
    description: string;
    location: string | null;
    contact_info: {
        phone?: string;
        email?: string;
        person?: string;
        address?: string;
        website?: string;
    } | null;
    images: string[];
    is_featured: boolean;
    created_at: string;
    updated_at: string;
}

interface Props {
    potential: Potential;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Potensi',
        href: '/admin/potentials',
    },
    {
        title: 'Detail Potensi',
        href: '#',
    },
];

export default function ShowPotential({ potential }: Props) {
    const getTypeLabel = (type: string) => {
        return type === 'tourism' ? 'Wisata' : 'UMKM';
    };

    const getTypeColor = (type: string) => {
        return type === 'tourism' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800';
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title={`${potential.name} - Admin Desa Lemah Duhur`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <div className="flex items-center space-x-3">
                            <h1 className="text-3xl font-bold text-gray-900">{potential.name}</h1>
                            <Badge className={getTypeColor(potential.type)}>{getTypeLabel(potential.type)}</Badge>
                            {potential.is_featured && (
                                <Badge variant="outline" className="border-yellow-300 text-yellow-600">
                                    <Star className="mr-1 h-3 w-3 fill-current" />
                                    Unggulan
                                </Badge>
                            )}
                        </div>
                        <p className="text-gray-600">Detail informasi potensi desa</p>
                    </div>
                    <div className="flex space-x-2">
                        <Link href={route('admin.potentials.edit', potential.id)}>
                            <Button className="flex items-center space-x-2">
                                <Edit className="h-4 w-4" />
                                <span>Edit</span>
                            </Button>
                        </Link>
                        <Link href={route('admin.potentials.index')}>
                            <Button variant="outline" className="flex items-center space-x-2">
                                <ArrowLeft className="h-4 w-4" />
                                <span>Kembali</span>
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Main Content */}
                    <div className="space-y-6 lg:col-span-2">
                        {/* Basic Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <MapPin className="h-5 w-5" />
                                    <span>Informasi Dasar</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Nama Potensi</label>
                                        <p className="text-gray-900">{potential.name}</p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Tipe</label>
                                        <p className="text-gray-900">{getTypeLabel(potential.type)}</p>
                                    </div>
                                </div>

                                {potential.location && (
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Lokasi</label>
                                        <p className="text-gray-900">{potential.location}</p>
                                    </div>
                                )}

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Deskripsi</label>
                                    <div className="mt-1 whitespace-pre-wrap text-gray-900">{potential.description}</div>
                                </div>

                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Dibuat</label>
                                        <p className="text-gray-900">
                                            {new Date(potential.created_at).toLocaleDateString('id-ID', {
                                                year: 'numeric',
                                                month: 'long',
                                                day: 'numeric',
                                                hour: '2-digit',
                                                minute: '2-digit',
                                            })}
                                        </p>
                                    </div>
                                    <div>
                                        <label className="text-sm font-medium text-gray-500">Terakhir Diperbarui</label>
                                        <p className="text-gray-900">
                                            {new Date(potential.updated_at).toLocaleDateString('id-ID', {
                                                year: 'numeric',
                                                month: 'long',
                                                day: 'numeric',
                                                hour: '2-digit',
                                                minute: '2-digit',
                                            })}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Images */}
                        {potential.images.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                        <ImageIcon className="h-5 w-5" />
                                        <span>Galeri Foto ({potential.images.length})</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                                        {potential.images.map((image, index) => (
                                            <div key={index} className="aspect-square overflow-hidden rounded-lg">
                                                <img
                                                    src={`/storage/${image}`}
                                                    alt={`${potential.name} - Foto ${index + 1}`}
                                                    className="h-full w-full object-cover transition-transform hover:scale-105"
                                                />
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Contact Information */}
                        {potential.contact_info && Object.values(potential.contact_info).some((value) => value) && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Informasi Kontak</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {potential.contact_info.person && (
                                        <div className="flex items-start space-x-3">
                                            <User className="mt-0.5 h-5 w-5 text-gray-400" />
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Penanggung Jawab</label>
                                                <p className="text-gray-900">{potential.contact_info.person}</p>
                                            </div>
                                        </div>
                                    )}

                                    {potential.contact_info.phone && (
                                        <div className="flex items-start space-x-3">
                                            <Phone className="mt-0.5 h-5 w-5 text-gray-400" />
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Telepon</label>
                                                <p className="text-gray-900">{potential.contact_info.phone}</p>
                                            </div>
                                        </div>
                                    )}

                                    {potential.contact_info.email && (
                                        <div className="flex items-start space-x-3">
                                            <Mail className="mt-0.5 h-5 w-5 text-gray-400" />
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Email</label>
                                                <p className="text-gray-900">{potential.contact_info.email}</p>
                                            </div>
                                        </div>
                                    )}

                                    {potential.contact_info.website && (
                                        <div className="flex items-start space-x-3">
                                            <Globe className="mt-0.5 h-5 w-5 text-gray-400" />
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Website</label>
                                                <a
                                                    href={potential.contact_info.website}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-blue-600 underline hover:text-blue-800"
                                                >
                                                    {potential.contact_info.website}
                                                </a>
                                            </div>
                                        </div>
                                    )}

                                    {potential.contact_info.address && (
                                        <div className="flex items-start space-x-3">
                                            <MapPin className="mt-0.5 h-5 w-5 text-gray-400" />
                                            <div>
                                                <label className="text-sm font-medium text-gray-500">Alamat</label>
                                                <p className="text-gray-900">{potential.contact_info.address}</p>
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        )}

                        {/* Status */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Status</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-500">Tipe Potensi</span>
                                    <Badge className={getTypeColor(potential.type)}>{getTypeLabel(potential.type)}</Badge>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-500">Status Unggulan</span>
                                    {potential.is_featured ? (
                                        <Badge variant="outline" className="border-yellow-300 text-yellow-600">
                                            <Star className="mr-1 h-3 w-3 fill-current" />
                                            Unggulan
                                        </Badge>
                                    ) : (
                                        <Badge variant="outline" className="text-gray-500">
                                            Biasa
                                        </Badge>
                                    )}
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm font-medium text-gray-500">Jumlah Foto</span>
                                    <Badge variant="outline">{potential.images.length} foto</Badge>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
