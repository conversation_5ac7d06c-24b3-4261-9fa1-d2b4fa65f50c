import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem, type PaginatedData } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { Edit, Eye, FileText, Plus, Search, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Profile {
    id: number;
    section: string;
    title: string;
    content: string;
    image: string | null;
    order: number;
    created_at: string;
    updated_at: string;
}

interface Props {
    profiles: PaginatedData<Profile>;
    sections: Record<string, string>;
    filters: {
        section?: string;
        search?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Profil Desa',
        href: '/admin/profiles',
    },
];

export default function ProfilesIndex({ profiles, sections, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const { delete: destroy } = useForm();

    // Handle search with debounce
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (searchTerm !== filters.search) {
                router.get(
                    '/admin/profiles',
                    {
                        section: filters.section,
                        search: searchTerm || undefined,
                    },
                    {
                        preserveState: true,
                        replace: true,
                    },
                );
            }
        }, 300);

        return () => clearTimeout(timeoutId);
    }, [searchTerm, filters.search, filters.section]);

    const handleFilterChange = (key: string, value: string) => {
        router.get(
            '/admin/profiles',
            {
                ...filters,
                [key]: value || undefined,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleDelete = (profile: Profile) => {
        if (confirm(`Apakah Anda yakin ingin menghapus konten "${profile.title}"?`)) {
            destroy(route('admin.profiles.destroy', profile.id), {
                preserveScroll: true,
            });
        }
    };

    const getSectionBadgeColor = (section: string) => {
        const colors: Record<string, string> = {
            history: 'bg-blue-100 text-blue-800',
            vision_mission: 'bg-green-100 text-green-800',
            organization: 'bg-purple-100 text-purple-800',
            demographics: 'bg-orange-100 text-orange-800',
            geography: 'bg-teal-100 text-teal-800',
        };
        return colors[section] || 'bg-gray-100 text-gray-800';
    };

    const truncateContent = (content: string, maxLength: number = 100) => {
        if (content.length <= maxLength) return content;
        return content.substring(0, maxLength) + '...';
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Manajemen Profil Desa - Admin Desa Lemah Duhur" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Manajemen Profil Desa</h1>
                        <p className="text-gray-600">Kelola konten profil dan informasi desa</p>
                    </div>
                    <Link href={route('admin.profiles.create')}>
                        <Button className="flex items-center space-x-2">
                            <Plus className="h-4 w-4" />
                            <span>Tambah Konten</span>
                        </Button>
                    </Link>
                </div>

                {/* Quick Section Links */}
                <Card>
                    <CardHeader>
                        <CardTitle>Kelola per Bagian</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-5">
                            {Object.entries(sections).map(([key, title]) => (
                                <Link key={key} href={route('admin.profiles.section', key)}>
                                    <Button variant="outline" className="w-full justify-start">
                                        <FileText className="mr-2 h-4 w-4" />
                                        {title}
                                    </Button>
                                </Link>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Search className="h-5 w-5" />
                            <span>Filter & Pencarian</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Pencarian</label>
                                <Input placeholder="Cari judul atau konten..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Bagian</label>
                                <Select
                                    value={filters.section || 'all'}
                                    onValueChange={(value) => handleFilterChange('section', value === 'all' ? '' : value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Semua Bagian" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Bagian</SelectItem>
                                        {Object.entries(sections).map(([key, title]) => (
                                            <SelectItem key={key} value={key}>
                                                {title}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-end">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setSearchTerm('');
                                        router.get('/admin/profiles', {}, { replace: true });
                                    }}
                                    className="w-full"
                                >
                                    Reset Filter
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Profiles Table */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <FileText className="h-5 w-5" />
                                <span>Daftar Konten ({profiles.total})</span>
                            </div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {profiles.data.length > 0 ? (
                            <div className="space-y-4">
                                <div className="overflow-x-auto">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Judul</TableHead>
                                                <TableHead>Bagian</TableHead>
                                                <TableHead>Konten</TableHead>
                                                <TableHead>Urutan</TableHead>
                                                <TableHead>Gambar</TableHead>
                                                <TableHead>Tanggal Dibuat</TableHead>
                                                <TableHead className="text-right">Aksi</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {profiles.data.map((profile) => (
                                                <TableRow key={profile.id}>
                                                    <TableCell>
                                                        <div className="font-medium text-gray-900">{profile.title}</div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge className={getSectionBadgeColor(profile.section)}>{sections[profile.section]}</Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="max-w-xs text-sm text-gray-500">{truncateContent(profile.content)}</div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant="outline">{profile.order}</Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        {profile.image ? (
                                                            <div className="flex items-center space-x-2">
                                                                <img
                                                                    src={`/storage/${profile.image}`}
                                                                    alt={profile.title}
                                                                    className="h-8 w-8 rounded object-cover"
                                                                />
                                                                <span className="text-xs text-green-600">Ada</span>
                                                            </div>
                                                        ) : (
                                                            <span className="text-xs text-gray-400">Tidak ada</span>
                                                        )}
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="text-sm">{new Date(profile.created_at).toLocaleDateString('id-ID')}</div>
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex items-center justify-end space-x-2">
                                                            <Link href={route('admin.profiles.show', profile.id)}>
                                                                <Button variant="ghost" size="sm">
                                                                    <Eye className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Link href={route('admin.profiles.edit', profile.id)}>
                                                                <Button variant="ghost" size="sm">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => handleDelete(profile)}
                                                                className="text-red-600 hover:text-red-800"
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Pagination */}
                                {profiles.last_page > 1 && (
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm text-gray-500">
                                            Menampilkan {profiles.from} - {profiles.to} dari {profiles.total} konten
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {profiles.links.map((link, index) => (
                                                <Button
                                                    key={index}
                                                    variant={link.active ? 'default' : 'outline'}
                                                    size="sm"
                                                    disabled={!link.url}
                                                    onClick={() => {
                                                        if (link.url) {
                                                            router.get(link.url);
                                                        }
                                                    }}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="py-12 text-center">
                                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-4 text-lg font-medium text-gray-900">Belum ada konten profil</h3>
                                <p className="mt-2 text-gray-500">
                                    {Object.values(filters).some(Boolean)
                                        ? 'Tidak ada konten yang sesuai dengan filter yang dipilih.'
                                        : 'Mulai dengan membuat konten profil pertama untuk desa.'}
                                </p>
                                <div className="mt-6">
                                    <Link href={route('admin.profiles.create')}>
                                        <Button>
                                            <Plus className="mr-2 h-4 w-4" />
                                            Tambah Konten
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
