import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { ArrowLeft, Edit, Eye, FileText, GripVertical, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface Profile {
    id: number;
    section: string;
    title: string;
    content: string;
    image: string | null;
    order: number;
    created_at: string;
    updated_at: string;
}

interface Props {
    section: string;
    sectionTitle: string;
    profiles: Profile[];
}

export default function ManageSection({ section, sectionTitle, profiles }: Props) {
    const [items, setItems] = useState(profiles);
    const { delete: destroy } = useForm();

    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'Dashboard',
            href: '/dashboard',
        },
        {
            title: 'Manajemen Profil Desa',
            href: '/admin/profiles',
        },
        {
            title: sectionTitle,
            href: '#',
        },
    ];

    const handleDelete = (profile: Profile) => {
        if (confirm(`Apakah Anda yakin ingin menghapus konten "${profile.title}"?`)) {
            destroy(route('admin.profiles.destroy', profile.id), {
                preserveScroll: true,
                onSuccess: () => {
                    setItems(items.filter((item) => item.id !== profile.id));
                },
            });
        }
    };

    const moveItem = (index: number, direction: 'up' | 'down') => {
        const newItems = [...items];
        const targetIndex = direction === 'up' ? index - 1 : index + 1;

        if (targetIndex < 0 || targetIndex >= newItems.length) return;

        [newItems[index], newItems[targetIndex]] = [newItems[targetIndex], newItems[index]];

        // Update order values
        newItems.forEach((item, idx) => {
            item.order = idx + 1;
        });

        setItems(newItems);
    };

    const saveOrder = () => {
        const orderData = items.map((item, index) => ({
            id: item.id,
            order: index + 1,
        }));

        router.post(
            route('admin.profiles.section.order', section),
            {
                items: orderData,
            },
            {
                preserveScroll: true,
                onSuccess: () => {
                    // Order saved successfully
                },
            },
        );
    };

    const truncateContent = (content: string, maxLength: number = 150) => {
        if (content.length <= maxLength) return content;
        return content.substring(0, maxLength) + '...';
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title={`Kelola ${sectionTitle} - Admin Desa Lemah Duhur`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Kelola {sectionTitle}</h1>
                        <p className="text-gray-600">Atur konten dan urutan tampilan untuk bagian {sectionTitle.toLowerCase()}</p>
                    </div>
                    <div className="flex items-center space-x-3">
                        <Link href={route('admin.profiles.index')}>
                            <Button variant="outline" className="flex items-center space-x-2">
                                <ArrowLeft className="h-4 w-4" />
                                <span>Kembali</span>
                            </Button>
                        </Link>
                        <Link href={route('admin.profiles.create')} className="block">
                            <Button className="flex items-center space-x-2">
                                <Plus className="h-4 w-4" />
                                <span>Tambah Konten</span>
                            </Button>
                        </Link>
                    </div>
                </div>

                {/* Content List */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <FileText className="h-5 w-5" />
                                <span>Daftar Konten ({items.length})</span>
                            </div>
                            {items.length > 1 && (
                                <Button onClick={saveOrder} size="sm">
                                    Simpan Urutan
                                </Button>
                            )}
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {items.length > 0 ? (
                            <div className="space-y-4">
                                {items.map((profile, index) => (
                                    <div key={profile.id} className="flex items-start space-x-4 rounded-lg border p-4 hover:bg-gray-50">
                                        {/* Drag Handle & Order Controls */}
                                        <div className="flex flex-col items-center space-y-2">
                                            <GripVertical className="h-5 w-5 text-gray-400" />
                                            <div className="flex flex-col space-y-1">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => moveItem(index, 'up')}
                                                    disabled={index === 0}
                                                    className="h-6 w-6 p-0"
                                                >
                                                    ↑
                                                </Button>
                                                <span className="text-xs text-gray-500">{profile.order}</span>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => moveItem(index, 'down')}
                                                    disabled={index === items.length - 1}
                                                    className="h-6 w-6 p-0"
                                                >
                                                    ↓
                                                </Button>
                                            </div>
                                        </div>

                                        {/* Image */}
                                        <div className="flex-shrink-0">
                                            {profile.image ? (
                                                <img
                                                    src={`/storage/${profile.image}`}
                                                    alt={profile.title}
                                                    className="h-16 w-16 rounded-lg object-cover"
                                                />
                                            ) : (
                                                <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-gray-100">
                                                    <FileText className="h-6 w-6 text-gray-400" />
                                                </div>
                                            )}
                                        </div>

                                        {/* Content */}
                                        <div className="min-w-0 flex-1">
                                            <h3 className="font-medium text-gray-900">{profile.title}</h3>
                                            <p className="mt-1 text-sm text-gray-500">{truncateContent(profile.content)}</p>
                                            <div className="mt-2 text-xs text-gray-400">
                                                Dibuat: {new Date(profile.created_at).toLocaleDateString('id-ID')}
                                                {profile.updated_at !== profile.created_at && (
                                                    <span className="ml-2">
                                                        • Diperbarui: {new Date(profile.updated_at).toLocaleDateString('id-ID')}
                                                    </span>
                                                )}
                                            </div>
                                        </div>

                                        {/* Actions */}
                                        <div className="flex items-center space-x-2">
                                            <Link href={route('admin.profiles.show', profile.id)}>
                                                <Button variant="ghost" size="sm">
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </Link>
                                            <Link href={route('admin.profiles.edit', profile.id)}>
                                                <Button variant="ghost" size="sm">
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                            </Link>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleDelete(profile)}
                                                className="text-red-600 hover:text-red-800"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="py-12 text-center">
                                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-4 text-lg font-medium text-gray-900">Belum ada konten untuk {sectionTitle}</h3>
                                <p className="mt-2 text-gray-500">Mulai dengan membuat konten pertama untuk bagian ini.</p>
                                <div className="mt-6">
                                    <Link href={route('admin.profiles.create')}>
                                        <Button>
                                            <Plus className="mr-2 h-4 w-4" />
                                            Tambah Konten
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Section Info */}
                <Card>
                    <CardHeader>
                        <CardTitle>Informasi Bagian</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <div>
                                <h4 className="font-medium text-gray-900">Tips Konten {sectionTitle}</h4>
                                <div className="mt-2 text-sm text-gray-600">
                                    {section === 'history' && (
                                        <ul className="list-inside list-disc space-y-1">
                                            <li>Ceritakan sejarah pembentukan desa</li>
                                            <li>Sertakan tokoh-tokoh penting</li>
                                            <li>Tambahkan foto-foto bersejarah jika ada</li>
                                        </ul>
                                    )}
                                    {section === 'vision_mission' && (
                                        <ul className="list-inside list-disc space-y-1">
                                            <li>Visi harus jelas dan inspiratif</li>
                                            <li>Misi dalam bentuk poin-poin konkret</li>
                                            <li>Sesuaikan dengan kondisi desa saat ini</li>
                                        </ul>
                                    )}
                                    {section === 'organization' && (
                                        <ul className="list-inside list-disc space-y-1">
                                            <li>Sertakan foto dan jabatan perangkat desa</li>
                                            <li>Urutkan berdasarkan hierarki jabatan</li>
                                            <li>Perbarui jika ada pergantian perangkat</li>
                                        </ul>
                                    )}
                                    {section === 'demographics' && (
                                        <ul className="list-inside list-disc space-y-1">
                                            <li>Data penduduk berdasarkan usia dan gender</li>
                                            <li>Informasi pendidikan dan pekerjaan</li>
                                            <li>Perbarui data secara berkala</li>
                                        </ul>
                                    )}
                                    {section === 'geography' && (
                                        <ul className="list-inside list-disc space-y-1">
                                            <li>Lokasi dan batas wilayah desa</li>
                                            <li>Kondisi geografis dan topografi</li>
                                            <li>Sertakan peta jika memungkinkan</li>
                                        </ul>
                                    )}
                                </div>
                            </div>
                            <div>
                                <h4 className="font-medium text-gray-900">Statistik</h4>
                                <div className="mt-2 space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Total konten:</span>
                                        <span className="font-medium">{items.length}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Dengan gambar:</span>
                                        <span className="font-medium">{items.filter((item) => item.image).length}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Terakhir diperbarui:</span>
                                        <span className="font-medium">
                                            {items.length > 0
                                                ? new Date(Math.max(...items.map((item) => new Date(item.updated_at).getTime()))).toLocaleDateString(
                                                      'id-ID',
                                                  )
                                                : '-'}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
