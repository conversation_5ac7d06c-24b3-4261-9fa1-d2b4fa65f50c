import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem, type PaginatedData } from '@/types';
import { Head, Link, router, useForm } from '@inertiajs/react';
import { CheckCircle, Edit, Eye, Plus, Search, Settings, Trash2, XCircle } from 'lucide-react';
import { useEffect, useState } from 'react';

interface Service {
    id: number;
    name: string;
    description: string;
    cost: string | null;
    processing_time: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

interface Props {
    services: PaginatedData<Service>;
    filters: {
        status?: string;
        search?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Layanan',
        href: '/admin/services',
    },
];

export default function ServicesIndex({ services, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const { delete: destroy } = useForm();

    // Handle search with debounce
    useEffect(() => {
        const timeoutId = setTimeout(() => {
            if (searchTerm !== filters.search) {
                router.get(
                    '/admin/services',
                    {
                        status: filters.status,
                        search: searchTerm || undefined,
                    },
                    {
                        preserveState: true,
                        replace: true,
                    },
                );
            }
        }, 300);

        return () => clearTimeout(timeoutId);
    }, [searchTerm, filters.search, filters.status]);

    const handleFilterChange = (key: string, value: string) => {
        router.get(
            '/admin/services',
            {
                ...filters,
                [key]: value || undefined,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleDelete = (service: Service) => {
        if (confirm(`Apakah Anda yakin ingin menghapus layanan "${service.name}"?`)) {
            destroy(route('admin.services.destroy', service.id), {
                preserveScroll: true,
            });
        }
    };

    const handleToggleStatus = (service: Service) => {
        const action = service.is_active ? 'nonaktifkan' : 'aktifkan';
        if (confirm(`Apakah Anda yakin ingin ${action} layanan "${service.name}"?`)) {
            router.post(
                route('admin.services.toggle-status', service.id),
                {},
                {
                    preserveScroll: true,
                },
            );
        }
    };

    const formatCost = (cost: string | null) => {
        if (!cost) return 'Gratis';
        if (cost.toLowerCase() === 'gratis') return 'Gratis';
        return cost;
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Manajemen Layanan - Admin Desa Lemah Duhur" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Manajemen Layanan</h1>
                        <p className="text-gray-600">Kelola layanan publik desa</p>
                    </div>
                    <Link href={route('admin.services.create')}>
                        <Button className="flex items-center space-x-2">
                            <Plus className="h-4 w-4" />
                            <span>Tambah Layanan</span>
                        </Button>
                    </Link>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Search className="h-5 w-5" />
                            <span>Filter & Pencarian</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Pencarian</label>
                                <Input
                                    placeholder="Cari nama atau deskripsi layanan..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Status</label>
                                <Select
                                    value={filters.status || 'all'}
                                    onValueChange={(value) => handleFilterChange('status', value === 'all' ? '' : value)}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Semua Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Status</SelectItem>
                                        <SelectItem value="active">Aktif</SelectItem>
                                        <SelectItem value="inactive">Tidak Aktif</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="flex items-end">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setSearchTerm('');
                                        router.get('/admin/services', {}, { replace: true });
                                    }}
                                    className="w-full"
                                >
                                    Reset Filter
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Services Table */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                                <Settings className="h-5 w-5" />
                                <span>Daftar Layanan ({services.total})</span>
                            </div>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {services.data.length > 0 ? (
                            <div className="space-y-4">
                                <div className="overflow-x-auto">
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Nama Layanan</TableHead>
                                                <TableHead>Biaya</TableHead>
                                                <TableHead>Waktu Proses</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Tanggal Dibuat</TableHead>
                                                <TableHead className="text-right">Aksi</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {services.data.map((service) => (
                                                <TableRow key={service.id}>
                                                    <TableCell>
                                                        <div className="space-y-1">
                                                            <div className="font-medium text-gray-900">{service.name}</div>
                                                            <div className="line-clamp-2 text-sm text-gray-500">{service.description}</div>
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant="outline" className="font-mono">
                                                            {formatCost(service.cost)}
                                                        </Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <span className="text-sm">{service.processing_time}</span>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="flex items-center space-x-2">
                                                            {service.is_active ? (
                                                                <>
                                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                                    <span className="text-sm text-green-600">Aktif</span>
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <XCircle className="h-4 w-4 text-gray-400" />
                                                                    <span className="text-sm text-gray-500">Tidak Aktif</span>
                                                                </>
                                                            )}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        <div className="text-sm">{new Date(service.created_at).toLocaleDateString('id-ID')}</div>
                                                    </TableCell>
                                                    <TableCell className="text-right">
                                                        <div className="flex items-center justify-end space-x-2">
                                                            <Link href={route('admin.services.show', service.id)}>
                                                                <Button variant="ghost" size="sm">
                                                                    <Eye className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Link href={route('admin.services.edit', service.id)}>
                                                                <Button variant="ghost" size="sm">
                                                                    <Edit className="h-4 w-4" />
                                                                </Button>
                                                            </Link>
                                                            <Button variant="ghost" size="sm" onClick={() => handleToggleStatus(service)}>
                                                                {service.is_active ? (
                                                                    <XCircle className="h-4 w-4" />
                                                                ) : (
                                                                    <CheckCircle className="h-4 w-4" />
                                                                )}
                                                            </Button>
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => handleDelete(service)}
                                                                className="text-red-600 hover:text-red-800"
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Pagination */}
                                {services.last_page > 1 && (
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm text-gray-500">
                                            Menampilkan {services.from} - {services.to} dari {services.total} layanan
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            {services.links.map((link, index) => (
                                                <Button
                                                    key={index}
                                                    variant={link.active ? 'default' : 'outline'}
                                                    size="sm"
                                                    disabled={!link.url}
                                                    onClick={() => {
                                                        if (link.url) {
                                                            router.get(link.url);
                                                        }
                                                    }}
                                                    dangerouslySetInnerHTML={{ __html: link.label }}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                        ) : (
                            <div className="py-12 text-center">
                                <Settings className="mx-auto h-12 w-12 text-gray-400" />
                                <h3 className="mt-4 text-lg font-medium text-gray-900">Belum ada layanan</h3>
                                <p className="mt-2 text-gray-500">
                                    {Object.values(filters).some(Boolean)
                                        ? 'Tidak ada layanan yang sesuai dengan filter yang dipilih.'
                                        : 'Mulai dengan membuat layanan pertama untuk desa.'}
                                </p>
                                <div className="mt-6">
                                    <Link href={route('admin.services.create')}>
                                        <Button>
                                            <Plus className="mr-2 h-4 w-4" />
                                            Tambah Layanan
                                        </Button>
                                    </Link>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
