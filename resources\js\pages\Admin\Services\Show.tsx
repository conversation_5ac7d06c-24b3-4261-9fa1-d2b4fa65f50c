import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, Clock, DollarSign, Edit, FileText, Phone, Settings, User, XCircle } from 'lucide-react';

interface Service {
    id: number;
    name: string;
    description: string;
    requirements: string[];
    procedure: string[];
    cost: string | null;
    processing_time: string;
    contact_info: {
        phone?: string;
        email?: string;
        person?: string;
    } | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
}

interface Props {
    service: Service;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
    {
        title: 'Manajemen Layanan',
        href: '/admin/services',
    },
    {
        title: 'Detail <PERSON>anan',
        href: '#',
    },
];

export default function ShowService({ service }: Props) {
    const formatCost = (cost: string | null) => {
        if (!cost) return 'Gratis';
        if (cost.toLowerCase() === 'gratis') return 'Gratis';
        return cost;
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title={`${service.name} - Admin Desa Lemah Duhur`} />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">{service.name}</h1>
                        <p className="text-gray-600">Detail informasi layanan</p>
                    </div>
                    <div className="flex items-center space-x-2">
                        <Link href={route('admin.services.edit', service.id)}>
                            <Button className="flex items-center space-x-2">
                                <Edit className="h-4 w-4" />
                                <span>Edit Layanan</span>
                            </Button>
                        </Link>
                        <Link href={route('admin.services.index')}>
                            <Button variant="outline" className="flex items-center space-x-2">
                                <ArrowLeft className="h-4 w-4" />
                                <span>Kembali</span>
                            </Button>
                        </Link>
                    </div>
                </div>

                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Main Content */}
                    <div className="space-y-6 lg:col-span-2">
                        {/* Basic Information */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <Settings className="h-5 w-5" />
                                    <span>Informasi Dasar</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <h3 className="font-medium text-gray-900">Deskripsi Layanan</h3>
                                    <p className="mt-1 text-gray-600">{service.description}</p>
                                </div>

                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div className="flex items-center space-x-2">
                                        <DollarSign className="h-4 w-4 text-gray-400" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">Biaya</p>
                                            <p className="text-sm text-gray-600">{formatCost(service.cost)}</p>
                                        </div>
                                    </div>

                                    <div className="flex items-center space-x-2">
                                        <Clock className="h-4 w-4 text-gray-400" />
                                        <div>
                                            <p className="text-sm font-medium text-gray-900">Waktu Proses</p>
                                            <p className="text-sm text-gray-600">{service.processing_time}</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Requirements */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <FileText className="h-5 w-5" />
                                    <span>Persyaratan Layanan</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <ol className="space-y-2">
                                    {service.requirements.map((requirement, index) => (
                                        <li key={index} className="flex items-start space-x-2">
                                            <span className="mt-1 flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-600">
                                                {index + 1}
                                            </span>
                                            <span className="text-gray-700">{requirement}</span>
                                        </li>
                                    ))}
                                </ol>
                            </CardContent>
                        </Card>

                        {/* Procedure */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <CheckCircle className="h-5 w-5" />
                                    <span>Prosedur Layanan</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <ol className="space-y-2">
                                    {service.procedure.map((step, index) => (
                                        <li key={index} className="flex items-start space-x-2">
                                            <span className="mt-1 flex h-5 w-5 items-center justify-center rounded-full bg-green-100 text-xs font-medium text-green-600">
                                                {index + 1}
                                            </span>
                                            <span className="text-gray-700">{step}</span>
                                        </li>
                                    ))}
                                </ol>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Status */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Status Layanan</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="flex items-center space-x-2">
                                    {service.is_active ? (
                                        <>
                                            <CheckCircle className="h-5 w-5 text-green-600" />
                                            <Badge variant="default" className="bg-green-100 text-green-800">
                                                Aktif
                                            </Badge>
                                        </>
                                    ) : (
                                        <>
                                            <XCircle className="h-5 w-5 text-gray-400" />
                                            <Badge variant="secondary">Tidak Aktif</Badge>
                                        </>
                                    )}
                                </div>
                                <p className="mt-2 text-sm text-gray-500">
                                    {service.is_active
                                        ? 'Layanan ini ditampilkan di halaman publik'
                                        : 'Layanan ini tidak ditampilkan di halaman publik'}
                                </p>
                            </CardContent>
                        </Card>

                        {/* Contact Information */}
                        {service.contact_info && Object.values(service.contact_info).some((value) => value) && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center space-x-2">
                                        <Phone className="h-5 w-5" />
                                        <span>Informasi Kontak</span>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-3">
                                    {service.contact_info.person && (
                                        <div className="flex items-center space-x-2">
                                            <User className="h-4 w-4 text-gray-400" />
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">Penanggung Jawab</p>
                                                <p className="text-sm text-gray-600">{service.contact_info.person}</p>
                                            </div>
                                        </div>
                                    )}

                                    {service.contact_info.phone && (
                                        <div className="flex items-center space-x-2">
                                            <Phone className="h-4 w-4 text-gray-400" />
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">Telepon</p>
                                                <p className="text-sm text-gray-600">{service.contact_info.phone}</p>
                                            </div>
                                        </div>
                                    )}

                                    {service.contact_info.email && (
                                        <div className="flex items-center space-x-2">
                                            <span className="h-4 w-4 text-gray-400">@</span>
                                            <div>
                                                <p className="text-sm font-medium text-gray-900">Email</p>
                                                <p className="text-sm text-gray-600">{service.contact_info.email}</p>
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        )}

                        {/* Metadata */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Informasi Sistem</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <div>
                                    <p className="text-sm font-medium text-gray-900">Dibuat</p>
                                    <p className="text-sm text-gray-600">
                                        {new Date(service.created_at).toLocaleDateString('id-ID', {
                                            weekday: 'long',
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                        })}
                                    </p>
                                </div>

                                <div>
                                    <p className="text-sm font-medium text-gray-900">Terakhir Diperbarui</p>
                                    <p className="text-sm text-gray-600">
                                        {new Date(service.updated_at).toLocaleDateString('id-ID', {
                                            weekday: 'long',
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                        })}
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
}
