import { Badge } from '@/components/ui/badge';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import AdminLayout from '@/layouts/AdminLayout';
import { type BreadcrumbItem } from '@/types';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Activity, Building2, Calendar, CheckCircle, Eye, FileText, MapPin, Settings, Star, TrendingUp, Users } from 'lucide-react';

interface Stats {
    total_news: number;
    published_news: number;
    draft_news: number;
    total_services: number;
    active_services: number;
    inactive_services: number;
    total_potentials: number;
    featured_potentials: number;
    tourism_potentials: number;
    umkm_potentials: number;
    total_users: number;
    profile_sections: number;
}

interface ActivityStats {
    news_this_month: number;
    services_updated: number;
    potentials_added: number;
}

interface RecentNews {
    id: number;
    title: string;
    slug: string;
    is_published: boolean;
    created_at: string;
    category: string;
}

interface RecentPotential {
    id: number;
    name: string;
    type: string;
    is_featured: boolean;
    created_at: string;
}

interface QuickAction {
    title: string;
    description: string;
    href: string;
    icon: string;
    color: string;
    count: number;
    label: string;
}

interface Props {
    stats: Stats;
    activity_stats: ActivityStats;
    recent_news: RecentNews[];
    recent_potentials: RecentPotential[];
    quick_actions: QuickAction[];
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

const iconMap = {
    FileText,
    Settings,
    MapPin,
    Building: Building2,
    Users,
};

export default function Dashboard({ stats, activity_stats, recent_news, recent_potentials, quick_actions }: Props) {
    const mainStatCards = [
        {
            title: 'Total Berita',
            value: stats.total_news,
            subtitle: `${stats.published_news} dipublikasikan, ${stats.draft_news} draft`,
            icon: FileText,
            color: 'text-blue-600',
            bgColor: 'bg-blue-50',
        },
        {
            title: 'Layanan Publik',
            value: stats.total_services,
            subtitle: `${stats.active_services} aktif, ${stats.inactive_services} nonaktif`,
            icon: Settings,
            color: 'text-green-600',
            bgColor: 'bg-green-50',
        },
        {
            title: 'Potensi Desa',
            value: stats.total_potentials,
            subtitle: `${stats.tourism_potentials} wisata, ${stats.umkm_potentials} UMKM`,
            icon: MapPin,
            color: 'text-purple-600',
            bgColor: 'bg-purple-50',
        },
        {
            title: 'Pengguna Sistem',
            value: stats.total_users,
            subtitle: `${stats.profile_sections} bagian profil`,
            icon: Users,
            color: 'text-orange-600',
            bgColor: 'bg-orange-50',
        },
    ];

    const activityCards = [
        {
            title: 'Berita Bulan Ini',
            value: activity_stats.news_this_month,
            icon: Calendar,
            color: 'text-blue-600',
        },
        {
            title: 'Layanan Diperbarui',
            value: activity_stats.services_updated,
            icon: Activity,
            color: 'text-green-600',
        },
        {
            title: 'Potensi Ditambahkan',
            value: activity_stats.potentials_added,
            icon: TrendingUp,
            color: 'text-purple-600',
        },
    ];

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard - Desa Lemah Duhur" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Welcome Header */}
                <div className="flex flex-col gap-2">
                    <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
                    <p className="text-gray-600">Selamat datang di panel administrasi Website Desa Lemah Duhur</p>
                </div>

                {/* Main Statistics */}
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                    {mainStatCards.map((stat, index) => {
                        const IconComponent = stat.icon;
                        return (
                            <Card key={index} className="transition-all hover:shadow-md">
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium text-gray-600">{stat.title}</CardTitle>
                                    <div className={`rounded-lg p-2 ${stat.bgColor}`}>
                                        <IconComponent className={`h-5 w-5 ${stat.color}`} />
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-3xl font-bold text-gray-900">{stat.value}</div>
                                    <p className="mt-1 text-xs text-gray-500">{stat.subtitle}</p>
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>

                {/* Activity Statistics */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Activity className="h-5 w-5 text-blue-600" />
                            <span>Aktivitas 30 Hari Terakhir</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                            {activityCards.map((activity, index) => {
                                const IconComponent = activity.icon;
                                return (
                                    <div key={index} className="flex items-center space-x-3 rounded-lg bg-gray-50 p-4">
                                        <IconComponent className={`h-6 w-6 ${activity.color}`} />
                                        <div>
                                            <div className="text-2xl font-bold text-gray-900">{activity.value}</div>
                                            <p className="text-sm text-gray-600">{activity.title}</p>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </CardContent>
                </Card>

                {/* Content Overview */}
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    {/* Recent News */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                    <FileText className="h-5 w-5 text-blue-600" />
                                    <span>Berita Terbaru</span>
                                </div>
                                <Link href="/admin/news" className="text-sm text-blue-600 hover:text-blue-800">
                                    Lihat Semua
                                </Link>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {recent_news.length > 0 ? (
                                    recent_news.map((news) => (
                                        <div key={news.id} className="flex items-start justify-between rounded-lg border p-3">
                                            <div className="flex-1">
                                                <h4 className="line-clamp-2 text-sm font-medium text-gray-900">{news.title}</h4>
                                                <div className="mt-2 flex items-center space-x-2">
                                                    <Badge variant="outline" className="text-xs">
                                                        {news.category}
                                                    </Badge>
                                                    <span className="text-xs text-gray-500">
                                                        {new Date(news.created_at).toLocaleDateString('id-ID')}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="ml-3">
                                                {news.is_published ? (
                                                    <CheckCircle className="h-4 w-4 text-green-600" />
                                                ) : (
                                                    <Eye className="h-4 w-4 text-gray-400" />
                                                )}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="py-8 text-center text-sm text-gray-500">Belum ada berita yang dibuat</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Potentials */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                    <MapPin className="h-5 w-5 text-purple-600" />
                                    <span>Potensi Terbaru</span>
                                </div>
                                <Link href="/potentials" className="text-sm text-purple-600 hover:text-purple-800">
                                    Lihat Semua
                                </Link>
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {recent_potentials.length > 0 ? (
                                    recent_potentials.map((potential) => (
                                        <div key={potential.id} className="flex items-start justify-between rounded-lg border p-3">
                                            <div className="flex-1">
                                                <h4 className="text-sm font-medium text-gray-900">{potential.name}</h4>
                                                <div className="mt-2 flex items-center space-x-2">
                                                    <Badge variant={potential.type === 'tourism' ? 'default' : 'secondary'} className="text-xs">
                                                        {potential.type === 'tourism' ? 'Wisata' : 'UMKM'}
                                                    </Badge>
                                                    <span className="text-xs text-gray-500">
                                                        {new Date(potential.created_at).toLocaleDateString('id-ID')}
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="ml-3">
                                                {potential.is_featured && <Star className="h-4 w-4 fill-current text-yellow-500" />}
                                            </div>
                                        </div>
                                    ))
                                ) : (
                                    <p className="py-8 text-center text-sm text-gray-500">Belum ada potensi yang ditambahkan</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <Settings className="h-5 w-5 text-green-600" />
                            <span>Aksi Cepat</span>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                            {quick_actions.map((action, index) => {
                                const IconComponent = iconMap[action.icon as keyof typeof iconMap];
                                const colorClasses = {
                                    blue: 'bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200',
                                    green: 'bg-green-50 hover:bg-green-100 text-green-600 border-green-200',
                                    purple: 'bg-purple-50 hover:bg-purple-100 text-purple-600 border-purple-200',
                                    orange: 'bg-orange-50 hover:bg-orange-100 text-orange-600 border-orange-200',
                                };

                                return (
                                    <Link
                                        key={index}
                                        href={action.href}
                                        className={`block rounded-lg border p-4 transition-all hover:shadow-md ${
                                            colorClasses[action.color as keyof typeof colorClasses]
                                        }`}
                                    >
                                        <div className="flex items-start space-x-3">
                                            <IconComponent className="h-6 w-6 flex-shrink-0" />
                                            <div className="flex-1">
                                                <h4 className="font-medium text-gray-900">{action.title}</h4>
                                                <p className="mt-1 text-sm text-gray-600">{action.description}</p>
                                                <div className="mt-2 flex items-center space-x-1">
                                                    <span className="text-lg font-bold text-gray-900">{action.count}</span>
                                                    <span className="text-xs text-gray-500">{action.label}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </Link>
                                );
                            })}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
