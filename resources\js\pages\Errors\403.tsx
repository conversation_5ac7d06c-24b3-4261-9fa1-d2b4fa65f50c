import { Button } from '@/components/ui/button';
import { Head } from '@inertiajs/react';
import { ArrowLeft, Shield } from 'lucide-react';

export default function Error403() {
    return (
        <>
            <Head title="403 - <PERSON><PERSON><PERSON>" />

            <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4">
                <div className="w-full max-w-md text-center">
                    <div className="mb-8">
                        <Shield className="mx-auto mb-4 h-24 w-24 text-red-500" />
                        <h1 className="mb-2 text-4xl font-bold text-gray-900">403</h1>
                        <h2 className="mb-4 text-xl font-semibold text-gray-700">Aks<PERSON></h2>
                        <p className="mb-8 text-gray-600">
                            Maaf, Anda tidak memiliki izin untuk mengakses halaman ini. Silakan hubungi administrator jika Anda merasa ini adalah
                            kes<PERSON>han.
                        </p>
                    </div>

                    <div className="space-y-4">
                        <Button onClick={() => window.history.back()} variant="outline" className="w-full">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Kembali
                        </Button>

                        <Button onClick={() => (window.location.href = '/')} className="w-full">
                            Ke Beranda
                        </Button>
                    </div>
                </div>
            </div>
        </>
    );
}
