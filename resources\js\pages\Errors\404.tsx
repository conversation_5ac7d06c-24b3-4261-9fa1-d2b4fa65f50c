import { Button } from '@/components/ui/button';
import { Head } from '@inertiajs/react';
import { ArrowLeft, Search } from 'lucide-react';

export default function Error404() {
    return (
        <>
            <Head title="404 - Halaman Tidak Ditemukan" />

            <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4">
                <div className="w-full max-w-md text-center">
                    <div className="mb-8">
                        <Search className="mx-auto mb-4 h-24 w-24 text-blue-500" />
                        <h1 className="mb-2 text-4xl font-bold text-gray-900">404</h1>
                        <h2 className="mb-4 text-xl font-semibold text-gray-700">Halaman Tidak Ditemukan</h2>
                        <p className="mb-8 text-gray-600">
                            Maaf, halaman yang Anda cari tidak dapat ditemukan. Mungkin halaman telah dipindahkan atau alamat yang Anda masukkan
                            salah.
                        </p>
                    </div>

                    <div className="space-y-4">
                        <Button onClick={() => window.history.back()} variant="outline" className="w-full">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Kembali
                        </Button>

                        <Button onClick={() => (window.location.href = '/')} className="w-full">
                            Ke Beranda
                        </Button>
                    </div>

                    <div className="mt-8 text-sm text-gray-500">
                        <p>Jika Anda yakin ini adalah kesalahan, silakan hubungi administrator desa.</p>
                    </div>
                </div>
            </div>
        </>
    );
}
