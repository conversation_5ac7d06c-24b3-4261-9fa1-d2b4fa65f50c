import { Button } from '@/components/ui/button';
import { Head } from '@inertiajs/react';
import { AlertTriangle, ArrowLeft } from 'lucide-react';

export default function Error500() {
    return (
        <>
            <Head title="500 - Kesalahan Server" />

            <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4">
                <div className="w-full max-w-md text-center">
                    <div className="mb-8">
                        <AlertTriangle className="mx-auto mb-4 h-24 w-24 text-red-500" />
                        <h1 className="mb-2 text-4xl font-bold text-gray-900">500</h1>
                        <h2 className="mb-4 text-xl font-semibold text-gray-700">Kesalahan Server</h2>
                        <p className="mb-8 text-gray-600">
                            Maaf, terjadi kesalahan pada server kami. Tim teknis telah diberitahu dan sedang memperbaiki masalah ini. <PERSON>lakan coba
                            lagi dalam beberapa saat.
                        </p>
                    </div>

                    <div className="space-y-4">
                        <Button onClick={() => window.location.reload()} variant="outline" className="w-full">
                            Muat Ulang Halaman
                        </Button>

                        <Button onClick={() => window.history.back()} variant="outline" className="w-full">
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Kembali
                        </Button>

                        <Button onClick={() => (window.location.href = '/')} className="w-full">
                            Ke Beranda
                        </Button>
                    </div>

                    <div className="mt-8 text-sm text-gray-500">
                        <p>Jika masalah berlanjut, silakan hubungi administrator desa di:</p>
                        <p className="mt-1 font-medium"><EMAIL></p>
                    </div>
                </div>
            </div>
        </>
    );
}
