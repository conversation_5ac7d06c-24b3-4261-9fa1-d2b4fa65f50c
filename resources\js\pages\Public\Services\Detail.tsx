import SEOHead from '@/components/Common/SEOHead';
import PublicLayout from '@/layouts/PublicLayout';
import { Link } from '@inertiajs/react';
import { ArrowLeft, CheckCircle, Clock, DollarSign, FileText, Mail, MapPin, Phone } from 'lucide-react';

interface Service {
    id: number;
    name: string;
    description: string;
    requirements: string[];
    procedure: string[];
    cost: string;
    processing_time: string;
    contact_info: {
        phone?: string;
        email?: string;
        address?: string;
        person?: string;
    };
}

interface ContactInfo {
    phone: string;
    email: string;
    address: string;
}

interface Props {
    service: Service;
    contactInfo: ContactInfo;
    seoMeta?: {
        title: string;
        description: string;
        image?: string;
        url?: string;
        type?: string;
        siteName?: string;
        locale?: string;
    };
    structuredData?: object;
}

export default function ServiceDetail({ service, contactInfo, seoMeta, structuredData }: Props) {
    return (
        <>
            <SEOHead
                title={seoMeta?.title}
                description={seoMeta?.description}
                image={seoMeta?.image}
                url={seoMeta?.url}
                type={seoMeta?.type}
                siteName={seoMeta?.siteName}
                locale={seoMeta?.locale}
                structuredData={structuredData}
            />
            <PublicLayout>
                <div className="min-h-screen bg-gray-50">
                    {/* Breadcrumb */}
                    <div className="border-b border-gray-200 bg-white">
                        <div className="container mx-auto px-4 py-4">
                            <Link href="/layanan" className="inline-flex items-center font-medium text-green-600 hover:text-green-700">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Kembali ke Daftar Layanan
                            </Link>
                        </div>
                    </div>

                    <div className="container mx-auto px-4 py-8">
                        <div className="grid gap-8 lg:grid-cols-3">
                            {/* Main Content */}
                            <div className="space-y-8 lg:col-span-2">
                                {/* Service Header */}
                                <div className="rounded-lg bg-white p-6 shadow-md">
                                    <h1 className="mb-4 text-3xl font-bold text-gray-900">{service.name}</h1>
                                    <p className="text-lg leading-relaxed text-gray-600">{service.description}</p>

                                    <div className="mt-6 flex items-center gap-6 border-t border-gray-200 pt-6">
                                        <div className="flex items-center">
                                            <DollarSign className="mr-2 h-5 w-5 text-green-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Biaya</p>
                                                <p className="text-lg font-semibold">{service.cost}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-center">
                                            <Clock className="mr-2 h-5 w-5 text-blue-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Waktu Proses</p>
                                                <p className="text-lg font-semibold">{service.processing_time}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Requirements */}
                                <div className="rounded-lg bg-white p-6 shadow-md">
                                    <h2 className="mb-4 flex items-center text-xl font-bold text-gray-900">
                                        <FileText className="mr-2 h-5 w-5 text-blue-600" />
                                        Persyaratan
                                    </h2>
                                    <ul className="space-y-3">
                                        {service.requirements.map((requirement, index) => (
                                            <li key={index} className="flex items-start">
                                                <CheckCircle className="mt-0.5 mr-3 h-5 w-5 flex-shrink-0 text-green-600" />
                                                <span className="text-gray-700">{requirement}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>

                                {/* Procedure */}
                                <div className="rounded-lg bg-white p-6 shadow-md">
                                    <h2 className="mb-4 text-xl font-bold text-gray-900">Prosedur Pelayanan</h2>
                                    <div className="space-y-4">
                                        {service.procedure.map((step, index) => (
                                            <div key={index} className="flex items-start">
                                                <div className="mr-4 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-green-600 text-sm font-bold text-white">
                                                    {index + 1}
                                                </div>
                                                <div className="flex-1">
                                                    <p className="text-gray-700">{step}</p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                {/* Additional Contact Info for Service */}
                                {service.contact_info && Object.keys(service.contact_info).length > 0 && (
                                    <div className="rounded-lg bg-white p-6 shadow-md">
                                        <h2 className="mb-4 text-xl font-bold text-gray-900">Kontak Khusus Layanan</h2>
                                        <div className="space-y-3">
                                            {service.contact_info.phone && (
                                                <div className="flex items-center">
                                                    <Phone className="mr-3 h-5 w-5 text-green-600" />
                                                    <span className="text-gray-700">{service.contact_info.phone}</span>
                                                </div>
                                            )}
                                            {service.contact_info.email && (
                                                <div className="flex items-center">
                                                    <Mail className="mr-3 h-5 w-5 text-green-600" />
                                                    <span className="text-gray-700">{service.contact_info.email}</span>
                                                </div>
                                            )}
                                            {service.contact_info.person && (
                                                <div className="flex items-center">
                                                    <span className="mr-3 h-5 w-5 font-bold text-green-600">PIC</span>
                                                    <span className="text-gray-700">{service.contact_info.person}</span>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Sidebar */}
                            <div className="space-y-6">
                                {/* Operational Hours */}
                                <div className="rounded-lg bg-white p-6 shadow-md">
                                    <h3 className="mb-4 flex items-center text-lg font-semibold text-gray-900">
                                        <Clock className="mr-2 h-5 w-5 text-green-600" />
                                        Jam Operasional
                                    </h3>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Senin - Jumat</span>
                                            <span className="font-medium">08:00 - 15:00 WIB</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Sabtu</span>
                                            <span className="font-medium">08:00 - 12:00 WIB</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Minggu</span>
                                            <span className="font-medium text-red-600">Tutup</span>
                                        </div>
                                        <div className="border-t border-gray-200 pt-2">
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Istirahat</span>
                                                <span className="font-medium">12:00 - 13:00 WIB</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Contact Information */}
                                <div className="rounded-lg bg-white p-6 shadow-md">
                                    <h3 className="mb-4 text-lg font-semibold text-gray-900">Informasi Kontak</h3>
                                    <div className="space-y-3">
                                        <div className="flex items-start">
                                            <Phone className="mt-0.5 mr-3 h-5 w-5 text-green-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Telepon</p>
                                                <p className="font-medium">{contactInfo.phone}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start">
                                            <Mail className="mt-0.5 mr-3 h-5 w-5 text-green-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Email</p>
                                                <p className="font-medium">{contactInfo.email}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start">
                                            <MapPin className="mt-0.5 mr-3 h-5 w-5 text-green-600" />
                                            <div>
                                                <p className="text-sm text-gray-600">Alamat</p>
                                                <p className="font-medium">{contactInfo.address}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Tips */}
                                <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-6">
                                    <h3 className="mb-2 text-lg font-semibold text-yellow-900">Tips Pelayanan</h3>
                                    <ul className="space-y-1 text-sm text-yellow-800">
                                        <li>• Datang sebelum jam 14:00 untuk pelayanan optimal</li>
                                        <li>• Siapkan fotokopi dokumen persyaratan</li>
                                        <li>• Bawa dokumen asli untuk verifikasi</li>
                                        <li>• Hubungi desa terlebih dahulu jika ada pertanyaan</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </PublicLayout>
        </>
    );
}
