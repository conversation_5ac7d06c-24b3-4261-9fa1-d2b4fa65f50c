import SEOHead from '@/components/Common/SEOHead';
import PublicLayout from '@/layouts/PublicLayout';
import { Link } from '@inertiajs/react';
import { ArrowRight, Clock, Mail, MapPin, Phone } from 'lucide-react';

interface Service {
    id: number;
    name: string;
    description: string;
    cost: string;
    processing_time: string;
}

interface OperationalHours {
    weekdays: string;
    saturday: string;
    sunday: string;
    break: string;
}

interface ContactInfo {
    phone: string;
    email: string;
    address: string;
}

interface Props {
    services: Service[];
    operationalHours: OperationalHours;
    contactInfo: ContactInfo;
    seoMeta?: {
        title: string;
        description: string;
        image?: string;
        url?: string;
        type?: string;
        siteName?: string;
        locale?: string;
    };
    structuredData?: object;
}

export default function ServicesIndex({ services, contactInfo, seoMeta, structuredData }: Props) {
    return (
        <>
            <SEOHead
                title={seoMeta?.title}
                description={seoMeta?.description}
                image={seoMeta?.image}
                url={seoMeta?.url}
                type={seoMeta?.type}
                siteName={seoMeta?.siteName}
                locale={seoMeta?.locale}
                structuredData={structuredData}
            />
            <PublicLayout>
                <div className="min-h-screen bg-gray-50">
                    {/* Hero Section */}
                    <div className="bg-gradient-to-r from-green-600 to-green-700 py-12 text-white sm:py-16">
                        <div className="container mx-auto px-3 sm:px-4">
                            <div className="mx-auto max-w-4xl text-center">
                                <h1 className="mb-4 text-2xl font-bold sm:text-3xl md:text-4xl lg:text-5xl">Layanan Publik</h1>
                                <p className="text-lg text-green-100 sm:text-xl md:text-2xl">Desa Lemah Duhur</p>
                                <p className="mx-auto mt-4 max-w-2xl px-4 text-sm text-green-100 sm:px-0 sm:text-base lg:text-lg">
                                    Kami menyediakan berbagai layanan administrasi untuk memudahkan warga dalam mengurus dokumen dan keperluan
                                    lainnya.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="container mx-auto px-3 py-8 sm:px-4 sm:py-12">
                        <div className="grid gap-6 sm:gap-8 lg:grid-cols-3">
                            {/* Services List */}
                            <div className="lg:col-span-2">
                                <h2 className="mb-4 text-xl font-bold text-gray-900 sm:mb-6 sm:text-2xl">Daftar Layanan Tersedia</h2>

                                <div className="grid gap-4 sm:gap-6">
                                    {services.map((service) => (
                                        <div
                                            key={service.id}
                                            className="touch-manipulation rounded-lg bg-white shadow-md transition-shadow duration-300 hover:shadow-lg"
                                        >
                                            <div className="p-4 sm:p-6">
                                                <div className="mb-3 flex flex-col gap-2 sm:mb-4 sm:flex-row sm:items-start sm:justify-between">
                                                    <h3 className="pr-2 text-lg font-semibold text-gray-900 sm:text-xl">{service.name}</h3>
                                                    <span className="self-start rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800">
                                                        {service.cost}
                                                    </span>
                                                </div>

                                                <p className="mb-4 line-clamp-3 text-sm text-gray-600 sm:text-base">{service.description}</p>

                                                <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
                                                    <div className="flex items-center text-xs text-gray-500 sm:text-sm">
                                                        <Clock className="mr-1 h-4 w-4 flex-shrink-0" />
                                                        <span>Proses: {service.processing_time}</span>
                                                    </div>

                                                    <Link
                                                        href={`/layanan/${service.id}`}
                                                        className="inline-flex touch-manipulation items-center py-2 text-sm font-medium text-green-600 hover:text-green-700 sm:py-0 sm:text-base"
                                                    >
                                                        Lihat Detail
                                                        <ArrowRight className="ml-1 h-4 w-4" />
                                                    </Link>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Sidebar */}
                            <div className="space-y-4 sm:space-y-6">
                                {/* Operational Hours */}
                                <div className="rounded-lg bg-white p-4 shadow-md sm:p-6">
                                    <h3 className="mb-3 flex items-center text-base font-semibold text-gray-900 sm:mb-4 sm:text-lg">
                                        <Clock className="mr-2 h-4 w-4 text-green-600 sm:h-5 sm:w-5" />
                                        Jam Operasional
                                    </h3>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Senin - Jumat</span>
                                            <span className="font-medium">08:00 - 15:00</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Sabtu</span>
                                            <span className="font-medium">08:00 - 12:00</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Minggu</span>
                                            <span className="font-medium text-red-600">Tutup</span>
                                        </div>
                                        <div className="mt-2 border-t border-gray-200 pt-2">
                                            <div className="flex justify-between">
                                                <span className="text-gray-600">Istirahat</span>
                                                <span className="font-medium">12:00 - 13:00</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Contact Information */}
                                <div className="rounded-lg bg-white p-4 shadow-md sm:p-6">
                                    <h3 className="mb-3 text-base font-semibold text-gray-900 sm:mb-4 sm:text-lg">Informasi Kontak</h3>
                                    <div className="space-y-3">
                                        <div className="flex items-start">
                                            <Phone className="mt-0.5 mr-3 h-4 w-4 flex-shrink-0 text-green-600 sm:h-5 sm:w-5" />
                                            <div className="min-w-0">
                                                <p className="text-sm text-gray-600">Telepon</p>
                                                <p className="font-medium break-all">{contactInfo.phone}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start">
                                            <Mail className="mt-0.5 mr-3 h-4 w-4 flex-shrink-0 text-green-600 sm:h-5 sm:w-5" />
                                            <div className="min-w-0">
                                                <p className="text-sm text-gray-600">Email</p>
                                                <p className="font-medium break-all">{contactInfo.email}</p>
                                            </div>
                                        </div>

                                        <div className="flex items-start">
                                            <MapPin className="mt-0.5 mr-3 h-4 w-4 flex-shrink-0 text-green-600 sm:h-5 sm:w-5" />
                                            <div className="min-w-0">
                                                <p className="text-sm text-gray-600">Alamat</p>
                                                <p className="font-medium break-words">{contactInfo.address}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Important Notice */}
                                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 sm:p-6">
                                    <h3 className="mb-2 text-base font-semibold text-blue-900 sm:text-lg">Penting!</h3>
                                    <p className="text-sm leading-relaxed text-blue-800">
                                        Pastikan Anda membawa semua persyaratan yang diperlukan sebelum datang ke kantor desa untuk mempercepat proses
                                        pelayanan.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </PublicLayout>
        </>
    );
}
