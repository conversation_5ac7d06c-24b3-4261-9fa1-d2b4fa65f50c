<?php

use App\Models\News;
use App\Models\Potential;
use App\Models\Profile;
use App\Models\Service;

describe('SEO Meta Tags and Sitemap', function () {
    beforeEach(function () {
        // Create test data
        News::factory()->published()->create([
            'title' => 'Berita SEO Test',
            'slug' => 'berita-seo-test',
            'meta_title' => 'Custom Meta Title',
            'meta_description' => 'Custom meta description for SEO',
        ]);

        Service::factory()->active()->create([
            'name' => 'Layanan SEO Test',
        ]);

        Potential::factory()->tourism()->create([
            'name' => 'Wisata SEO Test',
        ]);

        Profile::factory()->history()->create([
            'title' => 'Sejarah Desa',
        ]);
    });

    describe('Homepage SEO', function () {
        it('includes proper meta tags on homepage', function () {
            $response = $this->get('/');

            $response->assertInertia(fn ($page) => $page->has('seo')
                ->has('seo.title')
                ->has('seo.description')
                ->has('seo.keywords')
                ->where('seo.title', fn ($title) => str_contains($title, 'Desa Lemah Duhur')
                )
            );
        });

        it('includes Open Graph tags on homepage', function () {
            $response = $this->get('/');

            $response->assertInertia(fn ($page) => $page->has('seo.og_title')
                ->has('seo.og_description')
                ->has('seo.og_type')
                ->has('seo.og_url')
                ->where('seo.og_type', 'website')
            );
        });

        it('includes Twitter Card tags', function () {
            $response = $this->get('/');

            $response->assertInertia(fn ($page) => $page->has('seo.twitter_card')
                ->has('seo.twitter_title')
                ->has('seo.twitter_description')
                ->where('seo.twitter_card', 'summary_large_image')
            );
        });

        it('includes canonical URL', function () {
            $response = $this->get('/');

            $response->assertInertia(fn ($page) => $page->has('seo.canonical')
                ->where('seo.canonical', url('/'))
            );
        });
    });

    describe('News SEO', function () {
        it('includes custom meta tags for news article', function () {
            $response = $this->get('/berita/berita-seo-test');

            $response->assertInertia(fn ($page) => $page->has('seo')
                ->where('seo.title', 'Custom Meta Title')
                ->where('seo.description', 'Custom meta description for SEO')
            );
        });

        it('falls back to title and excerpt when meta tags not set', function () {
            $news = News::factory()->published()->create([
                'title' => 'Fallback Title Test',
                'slug' => 'fallback-title-test',
                'excerpt' => 'This is the excerpt for fallback',
                'meta_title' => null,
                'meta_description' => null,
            ]);

            $response = $this->get("/berita/{$news->slug}");

            $response->assertInertia(fn ($page) => $page->where('seo.title', 'Fallback Title Test')
                ->where('seo.description', 'This is the excerpt for fallback')
            );
        });

        it('includes article-specific Open Graph tags', function () {
            $response = $this->get('/berita/berita-seo-test');

            $response->assertInertia(fn ($page) => $page->has('seo.og_type')
                ->has('seo.og_article_published_time')
                ->has('seo.og_article_author')
                ->where('seo.og_type', 'article')
            );
        });

        it('includes featured image in meta tags', function () {
            $news = News::factory()->published()->withFeaturedImage()->create([
                'slug' => 'news-with-image',
            ]);

            $response = $this->get("/berita/{$news->slug}");

            $response->assertInertia(fn ($page) => $page->has('seo.og_image')
                ->has('seo.twitter_image')
            );
        });
    });

    describe('Profile Page SEO', function () {
        it('includes proper meta tags for profile page', function () {
            $response = $this->get('/profil');

            $response->assertInertia(fn ($page) => $page->has('seo')
                ->where('seo.title', fn ($title) => str_contains($title, 'Profil') && str_contains($title, 'Desa Lemah Duhur')
                )
                ->where('seo.description', fn ($desc) => str_contains($desc, 'profil') && str_contains($desc, 'desa')
                )
            );
        });

        it('includes structured data for organization', function () {
            $response = $this->get('/profil');

            $response->assertInertia(fn ($page) => $page->has('seo.structured_data')
                ->where('seo.structured_data', fn ($data) => isset($data['@type']) && $data['@type'] === 'Organization'
                )
            );
        });
    });

    describe('Services Page SEO', function () {
        it('includes proper meta tags for services page', function () {
            $response = $this->get('/layanan');

            $response->assertInertia(fn ($page) => $page->has('seo')
                ->where('seo.title', fn ($title) => str_contains($title, 'Layanan')
                )
                ->where('seo.description', fn ($desc) => str_contains($desc, 'layanan')
                )
            );
        });

        it('includes service-specific structured data', function () {
            $response = $this->get('/layanan');

            $response->assertInertia(fn ($page) => $page->has('seo.structured_data')
                ->where('seo.structured_data', fn ($data) => isset($data['@type']) && $data['@type'] === 'GovernmentService'
                )
            );
        });
    });

    describe('Potential Page SEO', function () {
        it('includes proper meta tags for potential page', function () {
            $response = $this->get('/potensi');

            $response->assertInertia(fn ($page) => $page->has('seo')
                ->where('seo.title', fn ($title) => str_contains($title, 'Potensi')
                )
                ->where('seo.description', fn ($desc) => str_contains($desc, 'potensi')
                )
            );
        });

        it('includes tourism-specific structured data', function () {
            $response = $this->get('/potensi');

            $response->assertInertia(fn ($page) => $page->has('seo.structured_data')
                ->where('seo.structured_data', fn ($data) => isset($data['@type']) && $data['@type'] === 'TouristAttraction'
                )
            );
        });
    });

    describe('Sitemap Generation', function () {
        it('generates XML sitemap successfully', function () {
            $response = $this->get('/sitemap.xml');

            $response->assertStatus(200);
            $response->assertHeader('Content-Type', 'application/xml; charset=UTF-8');
        });

        it('includes all main pages in sitemap', function () {
            $response = $this->get('/sitemap.xml');

            $content = $response->getContent();

            expect($content)->toContain('<loc>'.url('/').'</loc>');
            expect($content)->toContain('<loc>'.url('/profil').'</loc>');
            expect($content)->toContain('<loc>'.url('/layanan').'</loc>');
            expect($content)->toContain('<loc>'.url('/berita').'</loc>');
            expect($content)->toContain('<loc>'.url('/potensi').'</loc>');
        });

        it('includes published news articles in sitemap', function () {
            $response = $this->get('/sitemap.xml');

            $content = $response->getContent();

            expect($content)->toContain('<loc>'.url('/berita/berita-seo-test').'</loc>');
        });

        it('excludes unpublished news from sitemap', function () {
            $unpublishedNews = News::factory()->unpublished()->create([
                'slug' => 'unpublished-news',
            ]);

            $response = $this->get('/sitemap.xml');

            $content = $response->getContent();

            expect($content)->not()->toContain('<loc>'.url('/berita/unpublished-news').'</loc>');
        });

        it('includes lastmod dates in sitemap', function () {
            $response = $this->get('/sitemap.xml');

            $content = $response->getContent();

            expect($content)->toContain('<lastmod>');
        });

        it('includes priority and changefreq', function () {
            $response = $this->get('/sitemap.xml');

            $content = $response->getContent();

            expect($content)->toContain('<priority>');
            expect($content)->toContain('<changefreq>');
        });

        it('sets higher priority for homepage', function () {
            $response = $this->get('/sitemap.xml');

            $content = $response->getContent();

            // Homepage should have priority 1.0
            $homepageSection = $this->extractSitemapEntry($content, url('/'));
            expect($homepageSection)->toContain('<priority>1.0</priority>');
        });

        it('sets appropriate changefreq for different page types', function () {
            $response = $this->get('/sitemap.xml');

            $content = $response->getContent();

            // News should have higher change frequency
            $newsSection = $this->extractSitemapEntry($content, url('/berita'));
            expect($newsSection)->toContain('<changefreq>daily</changefreq>');

            // Profile should have lower change frequency
            $profileSection = $this->extractSitemapEntry($content, url('/profil'));
            expect($profileSection)->toContain('<changefreq>monthly</changefreq>');
        });
    });

    describe('Robots.txt', function () {
        it('serves robots.txt correctly', function () {
            $response = $this->get('/robots.txt');

            $response->assertStatus(200);
            $response->assertHeader('Content-Type', 'text/plain; charset=UTF-8');
        });

        it('includes sitemap reference in robots.txt', function () {
            $response = $this->get('/robots.txt');

            $content = $response->getContent();

            expect($content)->toContain('Sitemap: '.url('/sitemap.xml'));
        });

        it('allows all user agents by default', function () {
            $response = $this->get('/robots.txt');

            $content = $response->getContent();

            expect($content)->toContain('User-agent: *');
            expect($content)->toContain('Allow: /');
        });

        it('disallows admin areas', function () {
            $response = $this->get('/robots.txt');

            $content = $response->getContent();

            expect($content)->toContain('Disallow: /admin');
        });
    });

    describe('Meta Tags Validation', function () {
        it('ensures meta titles are within optimal length', function () {
            $response = $this->get('/');

            $response->assertInertia(fn ($page) => $page->where('seo.title', fn ($title) => strlen($title) >= 30 && strlen($title) <= 60
            )
            );
        });

        it('ensures meta descriptions are within optimal length', function () {
            $response = $this->get('/');

            $response->assertInertia(fn ($page) => $page->where('seo.description', fn ($desc) => strlen($desc) >= 120 && strlen($desc) <= 160
            )
            );
        });

        it('includes relevant keywords for village website', function () {
            $response = $this->get('/');

            $response->assertInertia(fn ($page) => $page->where('seo.keywords', fn ($keywords) => str_contains($keywords, 'desa') &&
                    str_contains($keywords, 'lemah duhur') &&
                    str_contains($keywords, 'caringin')
            )
            );
        });
    });

    describe('Performance', function () {
        it('generates sitemap efficiently', function () {
            // Create more content to test performance
            News::factory()->published()->count(100)->create();
            Service::factory()->active()->count(50)->create();
            Potential::factory()->count(30)->create();

            $start = microtime(true);

            $response = $this->get('/sitemap.xml');

            $end = microtime(true);
            $loadTime = $end - $start;

            $response->assertStatus(200);
            expect($loadTime)->toBeLessThan(2.0); // Should generate within 2 seconds
        });

        it('caches sitemap for performance', function () {
            // First request
            $response1 = $this->get('/sitemap.xml');

            // Second request should be faster (cached)
            $start = microtime(true);
            $response2 = $this->get('/sitemap.xml');
            $end = microtime(true);

            $loadTime = $end - $start;

            $response1->assertStatus(200);
            $response2->assertStatus(200);
            expect($loadTime)->toBeLessThan(0.5); // Cached response should be very fast
        });
    });

});

// Helper methods for SEOTest
function extractSitemapEntry(string $content, string $url): string
{
    $pattern = '/<url>.*?<loc>'.preg_quote($url, '/').'<\/loc>.*?<\/url>/s';
    preg_match($pattern, $content, $matches);

    return $matches[0] ?? '';
}
